server:
  host: 0.0.0.0
  port: 8004
  use_ngrok: false
  use_auth: false
  auth_username: user
  auth_password: password
  log_file_path: logs/tts_server.log
  log_file_max_size_mb: 10
  log_file_backup_count: 5
model:
  repo_id: ResembleAI/chatterbox
tts_engine:
  device: cuda
  predefined_voices_path: voices
  reference_audio_path: reference_audio
  default_voice_id: Emily.wav
paths:
  model_cache: model_cache
  output: outputs
generation_defaults:
  temperature: 0.8
  exaggeration: 1.3
  cfg_weight: 0.5
  seed: 0
  speed_factor: 1.0
  language: en
audio_output:
  format: wav
  sample_rate: 24000
  max_reference_duration_sec: 30
ui_state:
  last_text: 'Are you tired of slow, unreliable connections? Upgrade today to Quantum
    Fiber, the fastest internet in the galaxy! Experience seamless streaming, lag-free
    gaming, and instant downloads. Call now and get your first three months half price!
    Don''t wait, this offer won''t last forever!

    '
  last_voice_mode: predefined
  last_predefined_voice: Emily.wav
  last_reference_file: Gianna.wav
  last_seed: 3000
  last_chunk_size: 240
  last_split_text_enabled: true
  hide_chunk_warning: false
  hide_generation_warning: false
  theme: light
ui:
  title: Chatterbox TTS Server
  show_language_select: true
  max_predefined_voices_in_dropdown: 50
debug:
  save_intermediate_audio: false
