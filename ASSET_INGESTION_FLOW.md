# Asset Ingestion and Tagging Flow

This document explains how the blog-to-video pipeline properly ingests and tags assets at each step, ensuring proper categorization and approval tracking.

## 🎯 **Asset Flow Overview**

```
Blog Post → Script → Avatar Video → Final Branded Video
                      ↓ CONTENT      ↓ FINISHED + MARKETING
                   (approved)     (approved)
```

## 📋 **Step-by-Step Asset Ingestion**

### 1. **Script Generation** (ScriptGeneratorStateHandler)
- **Asset Created**: Text script (not ingested as media asset)
- **Approval**: Script approval tracked in pipeline state
- **Tags**: N/A (text content, not media asset)

### 2. **Avatar Video Generation** (AvatarGeneratorStateHandler)
- **Asset Created**: 30-second Creatify avatar video
- **Ingestion**: ✅ **Automatically ingested as 'content' asset**
- **Content Purpose**: `ContentPurpose.CONTENT`
- **Tags Applied**:
  ```typescript
  [
    'creatify-generated',
    'avatar-video', 
    'pipeline-content',
    'ai-generated',
    'avatar-{avatarId}',
    'voice-{voiceId}',
    'approved-content'  // ✅ Marked as approved
  ]
  ```
- **Location**: `AvatarGeneratorStateHandler.ingestAvatarVideoAsAsset()`

### 3. **Auto Composition** (AutoCompositionStateHandler + AutomaticVideoComposer)
- **Asset Created**: Final branded marketing video with intro/outro/overlays
- **Ingestion**: ✅ **Automatically ingested as 'finished' and 'marketing' asset**
- **Content Purpose**: `[ContentPurpose.FINISHED, ContentPurpose.MARKETING]`
- **Tags Applied**:
  ```typescript
  [
    'auto-composed',
    'finished',        // ✅ Tagged as finished
    'marketing'        // ✅ Tagged as marketing
  ]
  ```
- **Location**: `VideoIngestionService.ingestRenderedVideo()`

### 4. **Final Approval** (Pipeline State Management)
- **Additional Tags**: Approval tags added when user approves final video
- **Approval Tags**:
  ```typescript
  [
    'approved',
    'approved-at-final-approval',
    'approved-{date}'
  ]
  ```
- **Location**: `AutoCompositionStateHandler.addApprovalTags()`

## 🏗️ **Implementation Details**

### **Avatar Video Ingestion**
```typescript
// In AvatarGeneratorStateHandler.createAvatarVideo()
if (avatarVideo.status === 'complete' && avatarVideo.url) {
  try {
    await this.ingestAvatarVideoAsAsset(avatarVideo, scriptText);
  } catch (error) {
    console.warn('Failed to ingest avatar video as asset:', error);
    // Don't fail the entire process if ingestion fails
  }
}
```

**Key Features:**
- ✅ **Ingested as 'content'** - Ready for AutomaticVideoComposer
- ✅ **Approval tagged** - Marked as 'approved-content'
- ✅ **Metadata rich** - Includes script text, avatar ID, voice ID
- ✅ **Error resilient** - Ingestion failure doesn't break pipeline

### **Final Video Ingestion**
```typescript
// In VideoIngestionService.ingestRenderedVideo()
result.asset.tags = [
  ...result.asset.tags,
  'auto-composed',
  'finished',
  'marketing'
];

result.asset.contentPurpose = [
  ContentPurpose.FINISHED, 
  ContentPurpose.MARKETING
];
```

**Key Features:**
- ✅ **Dual purpose tagging** - Both 'finished' and 'marketing'
- ✅ **Auto-composition tags** - Tracks generation method
- ✅ **Ready for distribution** - Properly categorized for use

### **Approval Tracking**
```typescript
// In AutoCompositionStateHandler.addApprovalTags()
const approvalTags = [
  'approved',
  `approved-at-${approvalStep}`,
  `approved-${new Date().toISOString().split('T')[0]}`
];
```

**Key Features:**
- ✅ **Step tracking** - Records which step approved the asset
- ✅ **Date stamping** - Tracks when approval occurred
- ✅ **Unique tags** - Prevents duplicate approval tags

## 🔍 **Asset Query Examples**

### **Find All Content Assets (for AutomaticVideoComposer)**
```typescript
const contentAssets = await assetManager.getAssetsByPurpose(ContentPurpose.CONTENT);
// Returns: Avatar videos ready for composition
```

### **Find All Finished Marketing Videos**
```typescript
const marketingVideos = await assetManager.getAssetsByPurpose([
  ContentPurpose.FINISHED, 
  ContentPurpose.MARKETING
]);
// Returns: Final branded videos ready for distribution
```

### **Find All Approved Assets**
```typescript
const approvedAssets = await assetManager.getAssetsByTag('approved');
// Returns: All assets that have been approved at any step
```

### **Find Pipeline-Generated Content**
```typescript
const pipelineAssets = await assetManager.getAssetsByTag('pipeline-content');
// Returns: All assets generated by the blog-to-video pipeline
```

## 📊 **Asset Lifecycle Summary**

| Step | Asset Type | Purpose | Tags | Status |
|------|------------|---------|------|--------|
| **Script** | Text | N/A | N/A | Approved in state |
| **Avatar** | Video | `CONTENT` | `creatify-generated`, `approved-content` | ✅ Ingested |
| **Final** | Video | `FINISHED`, `MARKETING` | `auto-composed`, `finished`, `marketing` | ✅ Ingested |
| **Approved** | Video | `FINISHED`, `MARKETING` | + `approved`, `approved-at-final` | ✅ Tagged |

## 🚀 **Benefits of This Approach**

1. **Automatic Categorization**: Assets are properly tagged for their intended use
2. **Approval Tracking**: Complete audit trail of what was approved when
3. **Content Pipeline**: Avatar videos automatically become 'content' for composition
4. **Marketing Ready**: Final videos are immediately tagged for marketing use
5. **Error Resilience**: Ingestion failures don't break the pipeline
6. **Searchable**: Rich tagging enables powerful asset discovery
7. **Audit Trail**: Complete history of asset creation and approval

## 🔧 **Configuration**

The asset ingestion is configured through:
- **AssetManager**: Handles asset storage and retrieval
- **MediaIngestService**: Handles file ingestion and metadata extraction
- **VideoIngestionService**: Specialized for auto-composed video ingestion
- **ContentPurpose enum**: Defines asset categorization
- **Pipeline State**: Tracks approval status

This ensures that every asset created by the pipeline is properly categorized, tagged, and ready for its intended use in the marketing workflow.
