{"name": "ffmpeg-service", "version": "1.0.0", "description": "RESTful FFMPEG service for audio/video processing", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "watch": "ts-node --watch src/server.ts", "clean": "rm -rf dist", "prebuild": "npm run clean"}, "keywords": ["ffmpeg", "audio", "video", "processing", "rest", "api"], "author": "AutoMarket", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.24", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}