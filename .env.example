# AutoMarket - Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# MEDIA CREATION APIS
# =============================================================================

# REPLICATE API (for image generation)
# Get your token from https://replicate.com/account/api-tokens
REPLICATE_API_TOKEN=your_replicate_api_token_here

# FAL.AI API (for animations)
# Get your key from https://fal.ai/dashboard
FALAI_API_KEY=your_falai_api_key_here

# CREATIFY API (for avatar generation)
# Get your API credentials from https://creatify.ai/
CREATIFY_API_ID=your_creatify_api_id_here
CREATIFY_API_KEY=your_creatify_api_key_here

# Public versions (for client-side access)
NEXT_PUBLIC_CREATIFY_API_ID=your_creatify_api_id_here
NEXT_PUBLIC_CREATIFY_API_KEY=your_creatify_api_key_here

# =============================================================================
# SPEECH-TO-TEXT CONFIGURATION (WHISPER)
# =============================================================================

# Whisper STT Service (Docker container)
# Run: docker run -d -p 9000:9000 onerahmet/openai-whisper-asr-webservice:latest
WHISPER_STT_URL=http://localhost:9000
WHISPER_STT_ENABLED=true
WHISPER_STT_MAX_RETRIES=3
WHISPER_STT_RETRY_DELAY=1000

# =============================================================================
# TTS CONFIGURATION
# =============================================================================

# TTS Settings
TTS_ENABLED=true
TTS_PROVIDER=auto
TTS_VOICE=default
TTS_SPEED=1.0
TTS_EXAGGERATION=0.5
TTS_CFG_WEIGHT=0.5
TTS_MAX_LENGTH=50000

# Chatterbox TTS Configuration
CHATTERBOX_DOCKER_URL=http://localhost:8004

# Creatify TTS Configuration
CREATIFY_ACCENT=3480f048-8883-4bdc-b57f-4e7078e94b18

# Python path for TTS scripts
PYTHON_PATH=python3
OLLAMA_URL=http://localhost:11434

# =============================================================================
# NEXT.JS CONFIGURATION
# =============================================================================
# Set to production for production builds
NODE_ENV=development

# =============================================================================
# PIPELINE CONFIGURATION
# =============================================================================
# Default settings for the blog-to-video pipeline

# Video settings
DEFAULT_ASPECT_RATIO=16:9
DEFAULT_VIDEO_QUALITY=high

# Script generation settings
SCRIPT_TARGET_DURATION=45
SCRIPT_MAX_WORDS=125
SCRIPT_MIN_WORDS=75

# Avatar generation settings
AVATAR_POLLING_INTERVAL=5000
AVATAR_MAX_POLLING_ATTEMPTS=36

# Composition settings
COMPOSITION_INTRO_TEMPLATE=horizon-city-standard
COMPOSITION_OUTRO_TEMPLATE=horizon-city-standard
COMPOSITION_OVERLAY_TEMPLATE=horizon-city-branding
COMPOSITION_BRAND_COLOR=#8A2BE2

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# Local storage settings
PIPELINE_STORAGE_KEY=horizon-blog-to-video-pipelines
MAX_STORED_PIPELINES=50

# Video storage directory (relative to public/)
VIDEO_STORAGE_DIR=/videos

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable debug logging
DEBUG_PIPELINE=false
DEBUG_CREATIFY=false
DEBUG_OLLAMA=false

# Mock services for development (when APIs are not available)
MOCK_CREATIFY_API=false
MOCK_OLLAMA_API=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Add any security-related environment variables here
# Note: Never commit actual API keys to version control

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================
# Optional: Add analytics or monitoring service configurations
# VERCEL_ANALYTICS_ID=
# SENTRY_DSN=

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Production URL for absolute links
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable specific pipeline features
ENABLE_AI_SCRIPT_GENERATION=true
ENABLE_AVATAR_GENERATION=true
ENABLE_AUTO_COMPOSITION=true
ENABLE_PIPELINE_PERSISTENCE=true
ENABLE_BIDIRECTIONAL_NAVIGATION=true

# =============================================================================
# RATE LIMITING
# =============================================================================
# API rate limiting settings
CREATIFY_RATE_LIMIT_PER_MINUTE=10
OLLAMA_RATE_LIMIT_PER_MINUTE=30
PIPELINE_RATE_LIMIT_PER_HOUR=100

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
# Cache settings for improved performance
ENABLE_PIPELINE_CACHE=true
CACHE_TTL_SECONDS=3600
MAX_CACHE_SIZE_MB=100

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging levels: error, warn, info, debug
LOG_LEVEL=info
ENABLE_PIPELINE_AUDIT_LOG=true
ENABLE_API_REQUEST_LOG=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Automatic backup settings for pipeline data
ENABLE_AUTO_BACKUP=false
BACKUP_INTERVAL_HOURS=24
MAX_BACKUP_FILES=7
