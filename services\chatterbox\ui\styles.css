/*
 * Chatterbox TTS Server - Complete Compiled Stylesheet
 * This file contains all necessary CSS, manually compiled from the
 * original Tailwind CSS component definitions with @apply directives.
 */

/* --- 1. Tailwind CSS Base & Preflight Reset --- */
*,
::before,
::after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb;
}

::before,
::after {
    --tw-content: "";
}

html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-feature-settings: normal;
    font-variation-settings: normal;
}

body {
    margin: 0;
    line-height: inherit;
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
}

abbr:where([title]) {
    text-decoration: underline dotted;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit;
}

a {
    color: inherit;
    text-decoration: inherit;
}

b,
strong {
    font-weight: bolder;
}

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 1em;
}

small {
    font-size: 80%;
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sub {
    bottom: -0.25em;
}

sup {
    top: -0.5em;
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    color: inherit;
    margin: 0;
    padding: 0;
}

button,
select {
    text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none;
}

:-moz-focusring {
    outline: auto;
}

:-moz-ui-invalid {
    box-shadow: none;
}

progress {
    vertical-align: baseline;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto;
}

[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
}

::-webkit-search-decoration {
    -webkit-appearance: none;
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
}

summary {
    display: list-item;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0;
}

fieldset {
    margin: 0;
    padding: 0;
}

legend {
    padding: 0;
}

ol,
ul,
menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

dialog {
    padding: 0;
}

textarea {
    resize: vertical;
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    color: #9ca3af;
}

button,
[role="button"] {
    cursor: pointer;
}

:disabled {
    cursor: default;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    vertical-align: middle;
}

img,
video {
    max-width: 100%;
    height: auto;
}

[hidden] {
    display: none;
}

/* --- 2. Custom Component Styles (Light Mode Default) --- */

.body-base {
    height: 100%;
    background-color: #f8fafc;
    /* bg-slate-50 */
    color: #1e293b;
    /* text-slate-800 */
    transition-property: color, background-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.nav-base {
    background-color: #ffffff;
    /* bg-white */
    border-bottom-width: 1px;
    border-color: #e2e8f0;
    /* border-slate-200 */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    /* shadow-sm */
    position: sticky;
    top: 0;
    z-index: 40;
    transition-property: color, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.nav-link {
    color: #475569;
    /* text-slate-600 */
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.nav-link:hover {
    color: #4f46e5;
    /* hover:text-indigo-600 */
}

.title-link {
    color: #0f172a;
    /* text-slate-900 */
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 700;
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.title-link:hover {
    color: #4338ca;
    /* hover:text-indigo-700 */
}

.card-base {
    background-color: #ffffff;
    /* bg-white */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    /* shadow-lg */
    border-radius: 0.75rem;
    /* rounded-xl */
    overflow: hidden;
    border-width: 1px;
    border-color: rgba(226, 232, 240, 0.8);
    /* border-slate-200/80 */
    transition-property: color, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.card-header {
    font-size: 1.125rem;
    /* text-lg */
    line-height: 1.75rem;
    font-weight: 600;
    /* font-semibold */
    color: #1e293b;
    /* text-slate-800 */
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
    /* tracking-tight */
}

.card-footer {
    background-color: #f8fafc;
    /* bg-slate-50 */
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    border-top-width: 1px;
    border-color: #e2e8f0;
    /* border-slate-200 */
    transition-property: color, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.label-base {
    display: block;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    color: #334155;
    /* text-slate-700 */
    margin-bottom: 0.375rem;
    /* mb-1.5 */
}

.input-base,
.textarea-base,
.select-base {
    display: block;
    width: 100%;
    border-radius: 0.5rem;
    /* rounded-lg */
    border-width: 1px;
    border-color: #cbd5e1;
    /* border-slate-300 */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    /* shadow-sm */
    font-size: 0.875rem;
    line-height: 1.25rem;
    padding: 0.5rem 0.75rem;
    background-color: #f8fafc;
    /* bg-slate-50 */
    color: #0f172a;
    /* text-slate-900 */
    transition-property: border-color, box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.input-base::placeholder,
.textarea-base::placeholder {
    color: #94a3b8;
    /* placeholder-slate-400 */
}

.input-base:focus,
.textarea-base:focus,
.select-base:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    border-color: #6366f1;
    /* focus:border-indigo-500 */
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.4);
    /* focus:ring-indigo-500/40 */
}

.textarea-base {
    min-height: 100px;
}

.select-base {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding-right: 2.5rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2364748b' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
}

.button-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-width: 1px;
    border-color: transparent;
    border-radius: 0.5rem;
    /* rounded-lg */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    /* shadow-sm */
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    white-space: nowrap;
    flex-shrink: 0;
}

.button-base:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button-base:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #f8fafc, 0 0 0 4px rgba(99, 102, 241, 1);
    /* focus:ring-offset-slate-50 */
}

.btn-primary {
    background-color: #4f46e5;
    /* bg-indigo-600 */
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    /* Increased padding for better prominence */
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 600;
    /* Slightly bolder */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    /* Keep it rounded but not too much */
}

.btn-primary:hover {
    background-color: #4338ca;
    /* hover:bg-indigo-700 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    /* hover:shadow-lg */
    transform: translateY(-0.125rem);
    /* hover:-translate-y-0.5 */
}

.btn-primary:focus {
    box-shadow: 0 0 0 2px #f8fafc, 0 0 0 4px #6366f1;
    /* focus:ring-indigo-500 focus:ring-offset-slate-50 */
}

.btn-secondary {
    background-color: #ffffff;
    color: #334155;
    border-color: #b8bbbe;
    border-width: 1px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 600;
    border-radius: 0.5rem;
}

.btn-secondary:hover {
    background-color: #f8fafc;
    /* hover:bg-slate-50 */
}

.btn-secondary:focus {
    box-shadow: 0 0 0 2px #f8fafc, 0 0 0 4px #6366f1;
    /* focus:ring-indigo-500 focus:ring-offset-slate-50 */
}

.btn-danger {
    background-color: #dc2626;
    /* bg-red-600 */
    color: #ffffff;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-danger:hover {
    background-color: #b91c1c;
    /* hover:bg-red-700 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-0.125rem);
}

.btn-danger:focus {
    box-shadow: 0 0 0 2px #f8fafc, 0 0 0 4px #ef4444;
    /* focus:ring-red-500 focus:ring-offset-slate-50 */
}

.btn-purple {
    background-color: #9333ea;
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    /* Same as primary for consistency */
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 600;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
}

.btn-purple:hover {
    background-color: #7e22ce;
    /* hover:bg-purple-700 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-0.125rem);
}

.btn-purple:focus {
    box-shadow: 0 0 0 2px #f8fafc, 0 0 0 4px #a855f7;
    /* focus:ring-purple-500 focus:ring-offset-slate-50 */
}

.btn-import {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.4rem 1rem;
    border-width: 1px;
    border-color: #cbd5e1;
    border-radius: 0.5rem;
    background-color: #ffffff;
    color: #334155;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    gap: 0.375rem;
    /* Space between icon and text */
    min-width: 4rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.btn-import:hover {
    background-color: #f8fafc;
    border-color: #94a3b8;
}

.btn-import svg {
    width: 1.25rem;
    height: 1.25rem;
    margin: 0;
    /* Remove margin since we're using flex gap */
}

.btn-refresh {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.4rem 1rem;
    border-width: 1px;
    border-color: #cbd5e1;
    border-radius: 0.5rem;
    background-color: #ffffff;
    color: #334155;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    gap: 0.375rem;
    min-width: 4rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.btn-refresh:hover {
    background-color: #f8fafc;
    border-color: #94a3b8;
}

.btn-refresh svg {
    width: 1.25rem;
    height: 1.25rem;
}

.slider-base {
    width: 100%;
    height: 0.5rem;
    background-color: #e2e8f0;
    /* bg-slate-200 */
    border-radius: 9999px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    /* space-x-3 */
    cursor: pointer;
    border-width: 1px;
    border-color: #cbd5e1;
    /* border-slate-300 */
    background-color: #ffffff;
    padding: 1rem;
    border-radius: 0.5rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.radio-label:hover {
    border-color: #818cf8;
    /* hover:border-indigo-400 */
}

.radio-label-text {
    color: #334155;
    /* text-slate-700 */
    font-weight: 500;
}

.preset-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    background-color: #eef2ff;
    /* bg-indigo-100 */
    color: #4338ca;
    /* text-indigo-700 */
    transition-property: background-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    cursor: pointer;
}

.preset-button:hover {
    background-color: #e0e7ff;
    /* hover:bg-indigo-200 */
}

.preset-button:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 1px #ffffff, 0 0 0 3px #6366f1;
    /* focus:ring-indigo-500 focus:ring-offset-white */
}

.notification-base {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    border-width: 1px;
}

.notification-icon {
    height: 1.25rem;
    width: 1.25rem;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.notification-success {
    background-color: #f0fdf4;
    /* bg-green-50 */
    border-color: #bbf7d0;
    /* border-green-300 */
    color: #166534;
    /* text-green-800 */
}

.notification-success .notification-icon {
    color: #22c55e;
    /* text-green-500 */
}

.notification-error {
    background-color: #fef2f2;
    /* bg-red-50 */
    border-color: #fecaca;
    /* border-red-300 */
    color: #991b1b;
    /* text-red-800 */
}

.notification-error .notification-icon {
    color: #ef4444;
    /* text-red-500 */
}

.notification-warning {
    background-color: #fefce8;
    /* bg-yellow-50 */
    border-color: #fde047;
    /* border-yellow-300 */
    color: #854d0e;
    /* text-yellow-800 */
}

.notification-warning .notification-icon {
    color: #eab308;
    /* text-yellow-500 */
}

.notification-info {
    background-color: #eef2ff;
    /* bg-indigo-50 */
    border-color: #c7d2fe;
    /* border-indigo-300 */
    color: #3730a3;
    /* text-indigo-800 */
}

.notification-info .notification-icon {
    color: #6366f1;
    /* text-indigo-500 */
}

.code-inline {
    background-color: #f1f5f9;
    /* bg-slate-100 */
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    color: #4338ca;
    /* text-indigo-700 */
    border-width: 1px;
    border-color: #e2e8f0;
    /* border-slate-200 */
}

.tooltip {
    position: absolute;
    display: none;
    background-color: #334155;
    /* bg-slate-700 */
    color: #ffffff;
    font-size: 0.75rem;
    line-height: 1rem;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    z-index: 50;
    margin-top: -2rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.group:hover .tooltip {
    display: block;
}

.loading-overlay-base {
    position: fixed;
    inset: 0;
    background-color: rgba(75, 85, 99, 0.7);
    /* bg-slate-600/70 */
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    transition-property: opacity;
    transition-duration: 300ms;
}

.loading-box-base {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    align-items: center;
    border-width: 1px;
    border-color: #e2e8f0;
}

.loading-spinner {
    height: 2.5rem;
    width: 2.5rem;
    color: #4f46e5;
    /* text-indigo-600 */
    margin-bottom: 1.25rem;
}

.loading-text {
    color: #1e293b;
    /* text-slate-800 */
    font-size: 1.125rem;
    line-height: 1.75rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.loading-status {
    color: #475569;
    /* text-slate-600 */
    font-size: 0.875rem;
    line-height: 1.25rem;
    margin-bottom: 1.25rem;
    text-align: center;
    max-width: 20rem;
}

.waveform-container {
    width: 100%;
    height: 6rem;
    background-color: #f1f5f9;
    /* bg-slate-100 */
    border-radius: 0.5rem;
    border-width: 1px;
    border-color: #e2e8f0;
}

.audio-player-card {
    margin-top: 2rem;
}

.audio-player-controls {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.audio-player-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.audio-player-info {
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #475569;
    /* text-slate-600 */
    text-align: right;
}

.theme-switch-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 0.25rem;
    border-radius: 9999px;
    background-color: #e2e8f0;
    height: 2rem;
    width: 3.5rem;
    transition-property: background-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    border: none;
    cursor: pointer;
}

.theme-switch-button:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #6366f1;
}

.theme-switch-thumb {
    position: relative;
    border-radius: 9999px;
    width: 1.5rem;
    height: 1.5rem;
    background-color: #ffffff;
    transform: translateX(0);
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Sun icon (light mode) */
.theme-icon-sun {
    width: 1rem;
    height: 1rem;
    color: #f59e0b;
    /* text-amber-500 */
    opacity: 1;
    transition-property: opacity;
    transition-duration: 200ms;
}

/* Moon icon (dark mode) */
.theme-icon-moon {
    width: 1rem;
    height: 1rem;
    color: #6366f1;
    /* text-indigo-500 */
    opacity: 0;
    position: absolute;
    transition-property: opacity;
    transition-duration: 200ms;
}

.modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(15, 23, 42, 0.6);
    /* bg-slate-900/60 */
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.modal-box {
    background-color: #ffffff;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-width: 1px;
    border-color: #e2e8f0;
    max-width: 28rem;
    width: 100%;
}

.modal-title {
    font-size: 1.125rem;
    line-height: 1.75rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.modal-content {
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #475569;
    margin-bottom: 1.5rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* --- 3. Dark Mode Styles --- */

.dark .body-base {
    background-color: #0f172a;
    /* bg-slate-900 */
    color: #cbd5e1;
    /* text-slate-300 */
}

.dark .nav-base {
    background-image: linear-gradient(to right, #0f172a, #1e1b4b);
    /* bg-gradient-to-r from-slate-900 to-indigo-900 */
    border-color: rgba(51, 65, 85, 0.8);
    /* border-slate-700/80 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    /* shadow-md */
}

.dark .nav-link {
    color: #94a3b8;
    /* text-slate-400 */
}

.dark .nav-link:hover {
    color: #818cf8;
    /* hover:text-indigo-400 */
}

.dark .title-link {
    color: #f1f5f9;
    /* text-slate-100 */
}

.dark .title-link:hover {
    color: #818cf8;
    /* hover:text-indigo-400 */
}

.dark .card-base {
    background-color: #1e293b;
    /* bg-slate-800 */
    border-color: rgba(51, 65, 85, 0.8);
    /* border-slate-700/80 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    /* shadow-lg shadow-black/20 */
}

.dark .card-header {
    color: #f1f5f9;
    /* text-slate-100 */
}

.dark .card-footer {
    background-color: rgba(30, 41, 59, 0.5);
    /* bg-slate-800/50 */
    border-color: rgba(51, 65, 85, 0.8);
    /* border-slate-700/80 */
}

.dark .label-base {
    color: #94a3b8;
    /* text-slate-400 */
}

.dark .input-base,
.dark .textarea-base,
.dark .select-base {
    border-color: #475569;
    /* border-slate-600 */
    background-color: rgba(51, 65, 85, 0.5);
    /* bg-slate-700/50 */
    color: #f1f5f9;
    /* text-slate-100 */
}

.dark .input-base::placeholder,
.dark .textarea-base::placeholder {
    color: #64748b;
    /* placeholder-slate-500 */
}

.dark .input-base:focus,
.dark .textarea-base:focus,
.dark .select-base:focus {
    border-color: #818cf8;
    /* focus:border-indigo-400 */
    box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.5);
    /* focus:ring-indigo-400/50 */
}

.dark .select-base {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

.dark .btn-primary {
    background-color: #6366f1;
    /* bg-indigo-500 */
    color: #ffffff;
}

.dark .btn-primary:hover {
    background-color: #818cf8;
    /* hover:bg-indigo-400 */
}

.dark .btn-primary:focus {
    box-shadow: 0 0 0 2px #0f172a, 0 0 0 4px #818cf8;
    /* focus:ring-indigo-400 focus:ring-offset-slate-900 */
}

.dark .btn-secondary {
    background-color: #334155;
    /* bg-slate-700 */
    color: #e2e8f0;
    /* text-slate-200 */
    border-color: #475569;
    /* border-slate-600 */
}

.dark .btn-secondary:hover {
    background-color: #475569;
    /* hover:bg-slate-600 */
}

.dark .btn-secondary:focus {
    box-shadow: 0 0 0 2px #0f172a, 0 0 0 4px #818cf8;
    /* focus:ring-indigo-400 focus:ring-offset-slate-900 */
}

.dark .btn-danger {
    background-color: #dc2626;
    /* bg-red-600 */
    color: #ffffff;
}

.dark .btn-danger:hover {
    background-color: #f87171;
    /* hover:bg-red-500 */
}

.dark .btn-danger:focus {
    box-shadow: 0 0 0 2px #0f172a, 0 0 0 4px #ef4444;
    /* focus:ring-red-500 focus:ring-offset-slate-900 */
}

.dark .btn-purple {
    background-color: #a855f7;
    /* bg-purple-500 */
    color: #ffffff;
}

.dark .btn-purple:hover {
    background-color: #c084fc;
    /* hover:bg-purple-400 */
}

.dark .btn-purple:focus {
    box-shadow: 0 0 0 2px #0f172a, 0 0 0 4px #c084fc;
    /* focus:ring-purple-400 focus:ring-offset-slate-900 */
}

.dark .slider-base {
    background-color: #334155;
    /* bg-slate-700 */
}

.dark .radio-label {
    border-color: #475569;
    /* border-slate-600 */
    background-color: #1e293b;
    /* bg-slate-800 */
}

.dark .radio-label:hover {
    border-color: #6366f1;
    /* hover:border-indigo-500 */
}

.dark .radio-label-text {
    color: #cbd5e1;
    /* text-slate-300 */
}

.dark .preset-button {
    background-color: rgba(99, 102, 241, 0.1);
    /* bg-indigo-500/10 */
    color: #a5b4fc;
    /* text-indigo-300 */
}

.dark .preset-button:hover {
    background-color: rgba(99, 102, 241, 0.2);
    /* hover:bg-indigo-500/20 */
}

.dark .preset-button:focus {
    box-shadow: 0 0 0 2px #1e293b, 0 0 0 4px #818cf8;
    /* focus:ring-indigo-400 focus:ring-offset-slate-800 */
}

.dark .notification-success {
    background-color: #14532d;
    /* bg-green-950 */
    border-color: rgba(34, 197, 94, 0.5);
    /* border-green-700/50 */
    color: #bbf7d0;
    /* text-green-200 */
}

.dark .notification-success .notification-icon {
    color: #4ade80;
    /* text-green-400 */
}

.dark .notification-error {
    background-color: #7f1d1d;
    /* bg-red-950 */
    border-color: rgba(239, 68, 68, 0.5);
    /* border-red-700/50 */
    color: #fecaca;
    /* text-red-200 */
}

.dark .notification-error .notification-icon {
    color: #f87171;
    /* text-red-500 */
}

.dark .notification-warning {
    background-color: #78350f;
    /* bg-yellow-950 */
    border-color: rgba(234, 179, 8, 0.5);
    /* border-yellow-700/50 */
    color: #fde68a;
    /* text-yellow-200 */
}

.dark .notification-warning .notification-icon {
    color: #facc15;
    /* text-yellow-500 */
}

.dark .notification-info {
    background-color: #312e81;
    /* bg-indigo-950 */
    border-color: rgba(129, 140, 248, 0.5);
    /* border-indigo-700/50 */
    color: #c7d2fe;
    /* text-indigo-200 */
}

.dark .notification-info .notification-icon {
    color: #818cf8;
    /* text-indigo-400 */
}

.dark .code-inline {
    background-color: #334155;
    /* bg-slate-700 */
    color: #a5b4fc;
    /* text-indigo-300 */
    border-color: #475569;
    /* border-slate-600 */
}

.dark .tooltip {
    background-color: #0f172a;
    /* bg-slate-900 */
    color: #e2e8f0;
    /* text-slate-200 */
}

.dark .loading-overlay-base {
    background-color: rgba(15, 23, 42, 0.8);
    /* bg-slate-900/80 */
}

.dark .loading-box-base {
    background-color: #1e293b;
    /* bg-slate-800 */
    border-color: #334155;
    /* border-slate-700 */
}

.dark .loading-spinner {
    color: #6366f1;
    /* text-indigo-500 */
}

.dark .loading-text {
    color: #f1f5f9;
    /* text-slate-100 */
}

.dark .loading-status {
    color: #94a3b8;
    /* text-slate-400 */
}

.dark .waveform-container {
    background-color: rgba(30, 41, 59, 0.5);
    /* bg-slate-800/50 */
    border-color: #334155;
    /* border-slate-700 */
}

.dark .audio-player-info {
    color: #94a3b8;
    /* text-slate-400 */
}

.dark .theme-switch-button {
    background-color: #334155;
    /* bg-slate-700 */
}

.dark .theme-switch-button:focus {
    box-shadow: 0 0 0 2px #0f172a, 0 0 0 4px #6366f1;
    /* focus:ring-offset-slate-900 */
}

.dark .theme-switch-thumb {
    background-color: #6366f1;
    /* bg-indigo-500 */
    transform: translateX(1.5rem);
    /* translate-x-6 */
}

.dark .modal-box {
    background-color: #1e293b;
    /* bg-slate-800 */
    border-color: #334155;
    /* border-slate-700 */
}

.dark .modal-title {
    color: #f1f5f9;
    /* text-slate-100 */
}

.dark .modal-content {
    color: #cbd5e1;
    /* text-slate-300 */
}

/* --- 4. Specific Slider Styling --- */

input[type="range"].slider-base::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    background-color: #4f46e5;
    /* bg-indigo-600 */
    border-radius: 9999px;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: background-color 150ms ease-in-out;
}

input[type="range"].slider-base::-moz-range-thumb {
    width: 1.25rem;
    height: 1.25rem;
    background-color: #4f46e5;
    /* bg-indigo-600 */
    border-radius: 9999px;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: background-color 150ms ease-in-out;
}

.dark input[type="range"].slider-base::-webkit-slider-thumb {
    background-color: #6366f1;
    /* bg-indigo-500 */
    -webkit-appearance: none;
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 9999px;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.dark input[type="range"].slider-base::-moz-range-thumb {
    background-color: #6366f1;
    /* bg-indigo-500 */
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 9999px;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* --- 5. Additional Radio Button Styling --- */

.peer:checked+.radio-label {
    border-color: #6366f1;
    /* peer-checked:border-indigo-500 */
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.3);
    /* peer-checked:ring-indigo-500/30 */
}

.peer:checked+.radio-label .radio-label-text {
    color: #4f46e5;
    /* peer-checked:text-indigo-600 */
}

.dark .peer:checked+.radio-label {
    border-color: #6366f1;
    /* peer-checked:dark:border-indigo-500 */
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.4);
    /* peer-checked:dark:ring-indigo-500/40 */
}

.dark .peer:checked+.radio-label .radio-label-text {
    color: #818cf8;
    /* dark:peer-checked:text-indigo-400 */
}

/* --- 6. Animation Classes --- */

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* --- 7. Utility Classes --- */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.group-open\:rotate-180.group[open] {
    transform: rotate(180deg);
}

.tabular-nums {
    font-variant-numeric: tabular-nums;
}

.select-none {
    user-select: none;
}

.list-disc {
    list-style-type: disc;
}

.list-none {
    list-style-type: none;
}

.space-y-1> :not([hidden])~ :not([hidden]) {
    margin-top: 0.25rem;
}

.space-y-2\.5> :not([hidden])~ :not([hidden]) {
    margin-top: 0.625rem;
}

.space-y-3> :not([hidden])~ :not([hidden]) {
    margin-top: 0.75rem;
}

.space-x-3> :not([hidden])~ :not([hidden]) {
    margin-left: 0.75rem;
}

.space-x-2> :not([hidden])~ :not([hidden]) {
    margin-left: 0.5rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-x-6 {
    column-gap: 1.5rem;
}

.gap-y-4 {
    row-gap: 1rem;
}

.gap-y-5 {
    row-gap: 1.25rem;
}

.gap-x-2 {
    column-gap: 0.5rem;
}

.gap-6 {
    gap: 1.5rem;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-col {
    flex-direction: column;
}

.flex-grow {
    flex-grow: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.justify-end {
    justify-content: flex-end;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-flex-start {
    align-items: flex-start;
}

.self-center {
    align-self: center;
}

.w-full {
    width: 100%;
}

.w-32 {
    width: 8rem;
}

.w-36 {
    width: 9rem;
}

.w-10 {
    width: 2.5rem;
}

.w-4 {
    width: 1rem;
}

.w-5 {
    width: 1.25rem;
}

.w-6 {
    width: 1.5rem;
}

.h-full {
    height: 100%;
}

.h-4 {
    height: 1rem;
}

.h-5 {
    height: 1.25rem;
}

.h-6 {
    height: 1.5rem;
}

.h-10 {
    height: 2.5rem;
}

.h-16 {
    height: 4rem;
}

.h-2 {
    height: 0.5rem;
}

.h-24 {
    height: 6rem;
}

.min-h-full {
    min-height: 100%;
}

.max-w-7xl {
    max-width: 80rem;
}

.max-w-md {
    max-width: 28rem;
}

.max-w-xs {
    max-width: 20rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-5 {
    margin-top: 1.25rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-10 {
    margin-top: 2.5rem;
}

.mt-16 {
    margin-top: 4rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-5 {
    margin-bottom: 1.25rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.ml-1\.5 {
    margin-left: 0.375rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.mr-1\.5 {
    margin-right: 0.375rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-3 {
    margin-right: 0.75rem;
}

.-mt-8 {
    margin-top: -2rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.pt-5 {
    padding-top: 1.25rem;
}

.pl-5 {
    padding-left: 1.25rem;
}

.p-1 {
    padding: 0.25rem;
}

.p-4 {
    padding: 1rem;
}

.p-6 {
    padding: 1.5rem;
}

.p-8 {
    padding: 2rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
    margin-top: 3px;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.border {
    border-width: 1px;
}

.border-t {
    border-top-width: 1px;
}

.border-b {
    border-bottom-width: 1px;
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-full {
    border-radius: 9999px;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.opacity-0 {
    opacity: 0;
}

.opacity-25 {
    opacity: 0.25;
}

.opacity-75 {
    opacity: 0.75;
}

.opacity-100 {
    opacity: 1;
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-150 {
    transition-duration: 150ms;
}

.duration-200 {
    transition-duration: 200ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.sticky {
    position: sticky;
}

.inset-0 {
    inset: 0;
}

.top-0 {
    top: 0;
}

.bottom-2\.5 {
    bottom: 0.625rem;
}

.right-3 {
    right: 0.75rem;
}

.right-0\.5 {
    right: 0.125rem;
}

.z-40 {
    z-index: 40;
}

.z-50 {
    z-index: 50;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

.inline-flex {
    display: inline-flex;
}

.flex {
    display: flex;
}

.grid {
    display: grid;
}

.hidden {
    display: none;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.col-span-1 {
    grid-column: span 1 / span 1;
}

.col-span-2 {
    grid-column: span 2 / span 2;
}

.overflow-hidden {
    overflow: hidden;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-6 {
    --tw-translate-x: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-0\.5:hover {
    --tw-translate-y: -0.125rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* --- 8. Responsive Design --- */

@media (min-width: 640px) {
    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .sm\:p-8 {
        padding: 2rem;
    }

    .sm\:space-x-4> :not([hidden])~ :not([hidden]) {
        margin-left: 1rem;
    }

    .sm\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:col-span-2 {
        grid-column: span 2 / span 2;
    }

    .md\:flex-row {
        flex-direction: row;
    }

    .md\:w-auto {
        width: auto;
    }
}

@media (min-width: 1024px) {
    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* --- Voice Mode Selection Styling --- */

.voice-mode-option {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 1.5rem;
    border-width: 2px;
    border-color: #e2e8f0;
    /* border-slate-200 */
    background-color: #ffffff;
    color: #64748b;
    /* text-slate-500 */
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    text-align: center;
    position: relative;
}

.voice-mode-option:hover {
    border-color: #c7d2fe;
    /* hover:border-indigo-300 */
    background-color: #f8fafc;
    /* hover:bg-slate-50 */
}

/* Selected state for voice mode */
.voice-mode-option.selected {
    border-color: #4f46e5;
    /* border-indigo-600 */
    background-color: #eef2ff;
    /* bg-indigo-50 */
    color: #4f46e5;
    /* text-indigo-600 */
    box-shadow: 0 0 0 1px #4f46e5;
    /* Additional ring effect */
}


/* --- 6. Proper Button Styling for Settings Buttons --- */

.btn-settings {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-width: 1px;
    border-color: #cbd5e1;
    border-radius: 0.5rem;
    background-color: #ffffff;
    color: #374151;
    /* text-gray-700 */
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    cursor: pointer;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-settings:hover {
    background-color: #f9fafb;
    /* hover:bg-gray-50 */
    border-color: #9ca3af;
    /* hover:border-gray-400 */
}

.btn-settings:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #6366f1;
}

/* --- 7. Dark Mode Overrides for New Styles --- */

.dark .btn-primary {
    background-color: #6366f1;
    color: #ffffff;
}

.dark .btn-primary:hover {
    background-color: #818cf8;
}

.dark .btn-secondary {
    background-color: #334155;
    color: #e2e8f0;
    border-color: #475569;
}

.dark .btn-secondary:hover {
    background-color: #475569;
}

.dark .btn-purple {
    background-color: #a855f7;
    color: #ffffff;
}

.dark .btn-purple:hover {
    background-color: #c084fc;
}

.dark .btn-import,
.dark .btn-refresh {
    background-color: #1e293b;
    /* bg-slate-800 */
    color: #e2e8f0;
    /* text-slate-200 */
    border-color: #475569;
    /* border-slate-600 */
}

.dark .btn-import:hover,
.dark .btn-refresh:hover {
    background-color: #334155;
    /* hover:bg-slate-700 */
    border-color: #64748b;
    /* hover:border-slate-500 */
}

.dark .voice-mode-option {
    background-color: #1e293b;
    /* bg-slate-800 */
    border-color: #475569;
    /* border-slate-600 */
    color: #94a3b8;
    /* text-slate-400 */
}

.dark .voice-mode-option:hover {
    background-color: #334155;
    /* hover:bg-slate-700 */
    border-color: #64748b;
    /* hover:border-slate-500 */
}

.dark .voice-mode-option.selected {
    background-color: rgba(99, 102, 241, 0.1);
    /* bg-indigo-500/10 */
    border-color: #6366f1;
    /* border-indigo-500 */
    color: #a5b4fc;
    /* text-indigo-300 */
    box-shadow: 0 0 0 1px #6366f1;
}

.dark .theme-switch-button {
    background-color: #334155;
    /* bg-slate-700 */
}

.dark .theme-switch-thumb {
    background-color: #6366f1;
    /* bg-indigo-500 */
    transform: translateX(1.5rem);
    /* translate-x-6 */
}

.dark .theme-icon-sun {
    opacity: 0;
}

.dark .theme-icon-moon {
    opacity: 1;
    color: #ffffff;
}

.dark .btn-settings {
    background-color: #1e293b;
    /* bg-slate-800 */
    color: #e2e8f0;
    /* text-slate-200 */
    border-color: #475569;
    /* border-slate-600 */
}

.dark .btn-settings:hover {
    background-color: #334155;
    /* hover:bg-slate-700 */
    border-color: #64748b;
    /* hover:border-slate-500 */
}