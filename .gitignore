# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store
Thumbs.db

# Next.js build output
.next/
out/

# Test output and coverage
coverage/
.nyc_output/

# Video output files from tests
*.mp4
*.avi
*.mov
*.webm
*.mkv

# Exception: Allow test videos in test_videos directory
!test_videos/
!test_videos/*.mp4
!test_videos/*.avi
!test_videos/*.mov
!test_videos/*.webm
!test_videos/*.mkv

# Audio output files from tests
*.wav
*.mp3
*.flac
*.aac

# Exception: Allow sample audio in sample_audio directory
!sample_audio/
!sample_audio/*.wav
!sample_audio/*.mp3

# Image output files from tests (keep some for testing)
test-output-*.png
test-output-*.jpg
test-output-*.jpeg

# Temporary files
temp/
tmp/
*.tmp

# Docker volumes and data
docker-data/
volumes/

# Provider-specific cache/temp
cache/
.cache/
fal-cache/
replicate-cache/

# Model downloads
models/
*.gguf
*.bin

# API keys and sensitive config (additional protection)
*secret*
*key*
*token*
!**/types/*key*  # Allow TypeScript key type definitions

# Task files
# tasks.json
# tasks/ 