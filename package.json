{"name": "automarket", "version": "0.1.0", "private": true, "overrides": {"tr46": "5.1.1", "glob": "^11.0.2", "rimraf": "^6.0.1", "formidable": "^3.5.4", "superagent": "^10.2.1", "lodash.pick": "npm:@types/lodash.pick@^4.4.9"}, "scripts": {"dev": "npx next dev", "build": "tsc --noEmit && npx next build", "start": "npx next start", "lint": "npx next lint --fix", "lint:fix-all": "npx eslint --fix \"**/*.{js,jsx,ts,tsx}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:integration": "vitest run --config vitest.integration.config.ts", "verify-media": "node scripts/verify-media-database.js", "verify-media-ts": "ts-node scripts/verify-media-database.ts", "register-png": "ts-node scripts/register-png-discovery.ts", "bulk-import": "tsx scripts/bulk-video-import.ts"}, "dependencies": {"@babel/runtime": "^7.27.1", "@chakra-ui/avatar": "^2.3.0", "@chakra-ui/next-js": "^2.4.2", "@chakra-ui/react": "^3.17.0", "@chakra-ui/tag": "^3.1.1", "@emotion/react": "^11.14.0", "@fal-ai/client": "^1.5.0", "@fontsource/inter": "^5.2.5", "@fontsource/rajdhani": "^5.2.5", "@fontsource/share-tech-mono": "^5.2.5", "@gradio/client": "^1.15.2", "@tsavo/creatify-api-ts": "^1.4.0", "@types/js-cookie": "^3.0.6", "@types/node-fetch": "^2.6.12", "@types/xml2js": "^0.4.14", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.9.0", "cheerio": "^1.0.0", "commander": "^14.0.0", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "exifr": "^7.1.3", "fontkit": "^2.0.4", "form-data": "^4.0.3", "FormData": "^0.10.1", "formidable": "^3.5.4", "gray-matter": "^4.0.3", "js-cookie": "^3.0.5", "lru-cache": "^10.4.3", "next": "^15.3.0", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "punycode": "^2.3.1", "puppeteer": "^24.7.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "replicate": "^1.0.1", "rimraf": "^6.0.1", "sharp": "^0.34.2", "superagent": "^10.2.1", "ts-node": "^10.9.2", "uuid": "^11.1.0", "vitest": "^3.2.3", "xml2js": "^0.6.2", "zod": "^3.24.3"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.27.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "cypress": "^14.3.3", "eslint": "^8", "eslint-config-next": "^15.3.0", "eslint-plugin-react-hooks-addons": "^0.5.0", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^11.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^24.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-jest": "^29.3.2", "tsx": "^4.20.3", "typescript": "^5", "undici": "^7.10.0"}}