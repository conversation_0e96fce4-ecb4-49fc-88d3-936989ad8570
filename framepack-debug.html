<!DOCTYPE html><html lang="en" class="__variable_5e98b6 __variable_afcfa0 __variable_f0d962 __variable_512732"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/7e69aca6b073fa64-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/d8c14dc5fcaf3a63-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/e4ab94819b1ff12b-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/f20b81b98aac358f-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" as="image" href="https://storage.googleapis.com/falserverless/framepack/framepack.jpg"/><link rel="stylesheet" href="/_next/static/css/50b6d35d7a56d22a.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/18125fdf0299a978.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-3c607203c149e2d5.js"/><script src="/_next/static/chunks/fd9d1056-78cb362580defc7b.js" async=""></script><script src="/_next/static/chunks/52117-bffe300bf0f6df7d.js" async=""></script><script src="/_next/static/chunks/main-app-f328725729851441.js" async=""></script><script src="/_next/static/chunks/9da6db1e-c3308e728af630be.js" async=""></script><script src="/_next/static/chunks/95031-9b62a533099eace7.js" async=""></script><script src="/_next/static/chunks/37813-252fea9de889c305.js" async=""></script><script src="/_next/static/chunks/49095-d13a2f3baad06e93.js" async=""></script><script src="/_next/static/chunks/96845-ea33a2169b1c7df2.js" async=""></script><script src="/_next/static/chunks/29763-5ac3d808e0a9620d.js" async=""></script><script src="/_next/static/chunks/82957-aef5ae7f4ee19098.js" async=""></script><script src="/_next/static/chunks/39643-4b935f0b42608a0e.js" async=""></script><script src="/_next/static/chunks/68335-e855da731ea75d69.js" async=""></script><script src="/_next/static/chunks/4074-82e275ff6186196f.js" async=""></script><script src="/_next/static/chunks/56370-119ae49b3a84589b.js" async=""></script><script src="/_next/static/chunks/app/layout-2dff62644a11bb7d.js" async=""></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-HRNLRXZ833"></script><script src="/_next/static/chunks/27648-caa9b493a47dc629.js" async=""></script><script src="/_next/static/chunks/app/error-2b45736f961575e3.js" async=""></script><script src="/_next/static/chunks/69902-37f48df72b80a01b.js" async=""></script><script src="/_next/static/chunks/76798-45c36fef3e62663c.js" async=""></script><script src="/_next/static/chunks/28119-89b8f055927fcbad.js" async=""></script><script src="/_next/static/chunks/app/not-found-a2d64b30801b4a5e.js" async=""></script><script src="/_next/static/chunks/71632-69addefea7ce37b8.js" async=""></script><script src="/_next/static/chunks/26470-6c5f3171f6709917.js" async=""></script><script src="/_next/static/chunks/89105-f233706d7cccd674.js" async=""></script><script src="/_next/static/chunks/88447-702256d80b07eb23.js" async=""></script><script src="/_next/static/chunks/51755-09ce4f3b30cceebd.js" async=""></script><script src="/_next/static/chunks/92813-a57920cff6ad1371.js" async=""></script><script src="/_next/static/chunks/62266-393fbff85279c7d7.js" async=""></script><script src="/_next/static/chunks/85281-6654c5be68c11c67.js" async=""></script><script src="/_next/static/chunks/46516-70a7cb42b896b142.js" async=""></script><script src="/_next/static/chunks/12172-e0a6d2f5d47e9d0e.js" async=""></script><script src="/_next/static/chunks/61482-8483b2021864fc02.js" async=""></script><script src="/_next/static/chunks/18642-3631ad70a3aeac91.js" async=""></script><script src="/_next/static/chunks/73892-eb7b6ccbc0fd6e5e.js" async=""></script><script src="/_next/static/chunks/31229-42e6c705eb176915.js" async=""></script><script src="/_next/static/chunks/13590-f69849ac25e543d3.js" async=""></script><script src="/_next/static/chunks/71333-5f42f848925af109.js" async=""></script><script src="/_next/static/chunks/98489-a17f09fdd330277e.js" async=""></script><script src="/_next/static/chunks/app/models/%5BendpointId%5D/layout-93cf258556dc53bf.js" async=""></script><script src="/_next/static/chunks/55eb4b32-2e00cf239b0428f3.js" async=""></script><script src="/_next/static/chunks/37728-ac11742159386b0d.js" async=""></script><script src="/_next/static/chunks/53597-f7f9b94a2f1025b9.js" async=""></script><script src="/_next/static/chunks/56470-163b5a2d4c9d0a75.js" async=""></script><script src="/_next/static/chunks/77598-68f29a5770332f81.js" async=""></script><script src="/_next/static/chunks/26092-f2c8b6dd807de6d8.js" async=""></script><script src="/_next/static/chunks/29229-043c9cfe4b16078c.js" async=""></script><script src="/_next/static/chunks/99191-d2c2ef8c21ffe964.js" async=""></script><script src="/_next/static/chunks/88263-8333c74b0d352b20.js" async=""></script><script src="/_next/static/chunks/41041-eaaefc50183d521c.js" async=""></script><script src="/_next/static/chunks/35826-be7913f8d388e6a5.js" async=""></script><script src="/_next/static/chunks/94160-6b84624a83f5d264.js" async=""></script><script src="/_next/static/chunks/86772-e38b24914078b175.js" async=""></script><script src="/_next/static/chunks/18996-a42af8b4451c07c7.js" async=""></script><script src="/_next/static/chunks/519-feefe8605dc8ab1f.js" async=""></script><script src="/_next/static/chunks/68296-ff11535029d6dfbf.js" async=""></script><script src="/_next/static/chunks/35732-cb2a30b2b2a03a7e.js" async=""></script><script src="/_next/static/chunks/app/models/%5BendpointId%5D/page-350c892e7ff2b32a.js" async=""></script><link href="/favicon.png" rel="shortcut icon" type="image/x-icon"/><link href="/apple-touch-icon.png" rel="apple-touch-icon"/><link rel="sitemap" type="application/xml" title="fal.ai sitemap" href="/sitemap.xml"/><title>Framepack | Image to Video | fal.ai</title><meta name="description" content="Framepack is an efficient Image-to-video model that autoregressively generates videos."/><meta name="robots" content="index"/><meta name="application/ld+json" content="{&quot;@context&quot;:&quot;https://schema.org/&quot;,&quot;@type&quot;:&quot;WebAPI&quot;,&quot;identifier&quot;:&quot;fal-ai/framepack&quot;,&quot;url&quot;:&quot;https://fal.run/fal-ai/framepack&quot;,&quot;name&quot;:&quot;Framepack API served by fal.ai&quot;,&quot;description&quot;:&quot;Framepack is an efficient Image-to-video model that autoregressively generates videos.. Ready-to-use REST inference API, best in class performance, no coldstarts, fair pricing.&quot;,&quot;documentation&quot;:&quot;https://fal.ai/models/fal-ai/framepack/api&quot;,&quot;termsOfService&quot;:&quot;https://fal.ai/terms&quot;,&quot;provider&quot;:{&quot;@type&quot;:&quot;Organization&quot;,&quot;name&quot;:&quot;fal.ai Inc.&quot;,&quot;url&quot;:&quot;https://fal.ai&quot;}}"/><link rel="canonical" href="https://fal.ai/models/fal-ai/framepack"/><meta property="og:title" content="Framepack | Image to Video | fal.ai"/><meta property="og:description" content="Framepack is an efficient Image-to-video model that autoregressively generates videos."/><meta property="og:url" content="https://fal.ai/models/fal-ai/framepack"/><meta property="og:image" content="https://fal.ai/api/models/thumbnail/fal-ai/framepack"/><meta property="og:image:alt" content="Framepack | Image to Video | fal.ai"/><meta property="og:type" content="article"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@fal"/><meta name="twitter:title" content="Framepack | Image to Video | fal.ai"/><meta name="twitter:description" content="Framepack is an efficient Image-to-video model that autoregressively generates videos."/><meta name="twitter:image" content="https://fal.ai/api/models/thumbnail/fal-ai/framepack"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

gtag('config', 'G-HRNLRXZ833');</script><script>!function(t){var k="ko",i=(window.globalKoalaKey=window.globalKoalaKey||k);if(window[i])return;var ko=(window[i]=[]);["identify","track","removeListeners","on","off","qualify","ready"].forEach(function(t){ko[t]=function(){var n=[].slice.call(arguments);return n.unshift(t),ko.push(n),ko}});var n=document.createElement("script");n.async=!0,n.setAttribute("src","https://cdn.getkoala.com/v1/pk_f63728be455c83e0e4bf62a608599ea7eb65/sdk.js"),(document.body || document.head).appendChild(n)}();</script><script>!function(){try{var d=document.documentElement,c=d.classList;c.remove('light','dark');var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';c.add('dark')}else{d.style.colorScheme = 'light';c.add('light')}}else if(e){c.add(e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><!--$--><!--/$--><div class="flex flex-col"><div class="flex h-full min-h-screen flex-col"><div class="w-full flex-1 flex-col pb-24"><div class="border-b border-stroke-light"><div class="container mx-auto px-4 py-0 lg:py-0"><div class="mx-auto flex w-full flex-row py-3"><div class="flex w-full justify-between"><div class="flex flex-1 items-start justify-start lg:hidden"><div class="max-lg:hidden"><a href="/"><svg viewBox="0 0 120 48" fill="none" xmlns="http://www.w3.org/2000/svg" style="fill:currentColor" class="h-6"><path d="M120 6.46345V41.6633C120 47.2476 119.937 47.4986 119.248 47.4986H110.724C110.034 47.4986 109.909 47.2476 109.909 41.6633V6.46345C109.909 0.**********.034 0.**********.724 0.628174H119.248C119.937 0.********** 0.********** 6.46345Z"></path><path d="M96.0997 27.0431V25.7255C96.0997 22.5882 94.6581 21.3333 92.1509 21.3333C89.7065 21.3333 88.3902 22.651 88.0142 24.9725C87.8888 25.6627 87.9515 26.2274 87.5754 26.2274H79.1138C78.6124 26.2274 78.6124 26.1019 78.6124 25.6627C78.6124 22.3372 81.7463 15.4353 92.6524 15.4353C100.362 15.4353 105.878 18.5098 105.878 27.0431V39.5921C105.878 42.4783 107.445 46.5568 107.445 47.1215C107.445 47.3724 107.257 47.4979 107.068 47.4979H97.6667C97.2279 47.4979 97.1652 47.247 96.8518 45.6783L96.6011 44.4862C96.4131 43.545 96.2877 43.2313 95.9117 43.2313C95.4102 43.2313 95.0968 44.3607 93.7179 45.6783C92.2136 47.0587 90.3959 47.9999 87.262 47.9999C82.185 47.9999 77.6095 45.1136 77.6095 38.8391C77.6095 31.8117 83.0625 28.8 91.1481 28.4862C95.3476 28.298 96.0997 28.7372 96.0997 27.0431ZM96.0997 36.1411V34.8862C96.0997 33.6313 95.7863 33.2548 94.7208 33.3176L92.4017 33.4431C89.4558 33.6313 87.7008 35.1372 87.7008 37.9607C87.7008 40.7215 89.205 42.1019 91.3988 42.1019C93.8433 42.1019 96.0997 39.8431 96.0997 36.1411Z"></path><path d="M60.5509 23.6548C60.5509 22.4627 60.0495 22.3372 57.7304 22.3372H56.6021C56.1634 22.3372 56.1007 22.0234 56.1007 19.4509V19.0117C56.1007 16.4392 56.1634 16.1882 56.6021 16.1882H57.9184C60.2375 16.1882 60.5509 15.9999 60.5509 14.8705V10.9804C60.5509 3.6392 64.2489 0 71.3943 0C74.2148 0 76.1579 0.439212 76.4713 0.627448C76.5966 0.752936 76.5966 0.941172 76.5966 3.26273V4.07842C76.5966 6.46272 76.5966 7.02742 76.4086 7.02742C76.2206 7.02742 75.3431 6.46272 73.8388 6.46272C71.8331 6.46272 70.5795 7.46664 70.5795 10.9804V14.8705C70.5795 15.9999 71.0182 16.1882 73.8388 16.1882H76.6593C77.2234 16.1882 77.2234 16.4392 77.2234 19.0117V19.4509C77.2234 22.0234 77.1607 22.3372 76.722 22.3372H73.8388C71.0182 22.3372 70.5795 22.4627 70.5795 23.6548V41.6626C70.5795 47.2469 70.3914 47.4979 69.89 47.4979H61.1777C60.6762 47.4979 60.5509 47.2469 60.5509 41.6626V23.6548Z"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M30.1574 0.740479C30.9676 0.740479 31.6169 1.39923 31.6944 2.20567C32.3853 9.39891 38.1102 15.1234 45.3039 15.8142C46.1104 15.8917 46.7692 16.541 46.7692 17.3511V30.8959C46.7692 31.706 46.1104 32.3553 45.3039 32.4328C38.1102 33.1236 32.3853 38.8481 31.6944 46.0414C31.6169 46.8478 30.9676 47.5065 30.1574 47.5065H16.6118C15.8016 47.5065 15.1523 46.8478 15.0748 46.0414C14.384 38.8481 8.65901 33.1236 1.46528 32.4328C0.658799 32.3553 0 31.706 0 30.8959V17.3511C0 16.541 0.658803 15.8917 1.46529 15.8142C8.65902 15.1234 14.384 9.39891 15.0748 2.20567C15.1523 1.39923 15.8016 0.740479 16.6118 0.740479H30.1574ZM9.39037 24.0839C9.39037 31.865 15.6915 38.1728 23.4644 38.1728C31.2373 38.1728 37.5385 31.865 37.5385 24.0839C37.5385 16.3028 31.2373 9.99498 23.4644 9.99498C15.6915 9.99498 9.39037 16.3028 9.39037 24.0839Z"></path></svg></a></div></div><div class="flex items-center space-x-6"><div class="mx-auto"><span data-state="closed" style="-webkit-touch-callout:none"><a href="/"><svg viewBox="0 0 120 48" fill="none" xmlns="http://www.w3.org/2000/svg" style="fill:currentColor" class="h-7"><path d="M120 6.46345V41.6633C120 47.2476 119.937 47.4986 119.248 47.4986H110.724C110.034 47.4986 109.909 47.2476 109.909 41.6633V6.46345C109.909 0.**********.034 0.**********.724 0.628174H119.248C119.937 0.********** 0.********** 6.46345Z"></path><path d="M96.0997 27.0431V25.7255C96.0997 22.5882 94.6581 21.3333 92.1509 21.3333C89.7065 21.3333 88.3902 22.651 88.0142 24.9725C87.8888 25.6627 87.9515 26.2274 87.5754 26.2274H79.1138C78.6124 26.2274 78.6124 26.1019 78.6124 25.6627C78.6124 22.3372 81.7463 15.4353 92.6524 15.4353C100.362 15.4353 105.878 18.5098 105.878 27.0431V39.5921C105.878 42.4783 107.445 46.5568 107.445 47.1215C107.445 47.3724 107.257 47.4979 107.068 47.4979H97.6667C97.2279 47.4979 97.1652 47.247 96.8518 45.6783L96.6011 44.4862C96.4131 43.545 96.2877 43.2313 95.9117 43.2313C95.4102 43.2313 95.0968 44.3607 93.7179 45.6783C92.2136 47.0587 90.3959 47.9999 87.262 47.9999C82.185 47.9999 77.6095 45.1136 77.6095 38.8391C77.6095 31.8117 83.0625 28.8 91.1481 28.4862C95.3476 28.298 96.0997 28.7372 96.0997 27.0431ZM96.0997 36.1411V34.8862C96.0997 33.6313 95.7863 33.2548 94.7208 33.3176L92.4017 33.4431C89.4558 33.6313 87.7008 35.1372 87.7008 37.9607C87.7008 40.7215 89.205 42.1019 91.3988 42.1019C93.8433 42.1019 96.0997 39.8431 96.0997 36.1411Z"></path><path d="M60.5509 23.6548C60.5509 22.4627 60.0495 22.3372 57.7304 22.3372H56.6021C56.1634 22.3372 56.1007 22.0234 56.1007 19.4509V19.0117C56.1007 16.4392 56.1634 16.1882 56.6021 16.1882H57.9184C60.2375 16.1882 60.5509 15.9999 60.5509 14.8705V10.9804C60.5509 3.6392 64.2489 0 71.3943 0C74.2148 0 76.1579 0.439212 76.4713 0.627448C76.5966 0.752936 76.5966 0.941172 76.5966 3.26273V4.07842C76.5966 6.46272 76.5966 7.02742 76.4086 7.02742C76.2206 7.02742 75.3431 6.46272 73.8388 6.46272C71.8331 6.46272 70.5795 7.46664 70.5795 10.9804V14.8705C70.5795 15.9999 71.0182 16.1882 73.8388 16.1882H76.6593C77.2234 16.1882 77.2234 16.4392 77.2234 19.0117V19.4509C77.2234 22.0234 77.1607 22.3372 76.722 22.3372H73.8388C71.0182 22.3372 70.5795 22.4627 70.5795 23.6548V41.6626C70.5795 47.2469 70.3914 47.4979 69.89 47.4979H61.1777C60.6762 47.4979 60.5509 47.2469 60.5509 41.6626V23.6548Z"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M30.1574 0.740479C30.9676 0.740479 31.6169 1.39923 31.6944 2.20567C32.3853 9.39891 38.1102 15.1234 45.3039 15.8142C46.1104 15.8917 46.7692 16.541 46.7692 17.3511V30.8959C46.7692 31.706 46.1104 32.3553 45.3039 32.4328C38.1102 33.1236 32.3853 38.8481 31.6944 46.0414C31.6169 46.8478 30.9676 47.5065 30.1574 47.5065H16.6118C15.8016 47.5065 15.1523 46.8478 15.0748 46.0414C14.384 38.8481 8.65901 33.1236 1.46528 32.4328C0.658799 32.3553 0 31.706 0 30.8959V17.3511C0 16.541 0.658803 15.8917 1.46529 15.8142C8.65902 15.1234 14.384 9.39891 15.0748 2.20567C15.1523 1.39923 15.8016 0.740479 16.6118 0.740479H30.1574ZM9.39037 24.0839C9.39037 31.865 15.6915 38.1728 23.4644 38.1728C31.2373 38.1728 37.5385 31.865 37.5385 24.0839C37.5385 16.3028 31.2373 9.99498 23.4644 9.99498C15.6915 9.99498 9.39037 16.3028 9.39037 24.0839Z"></path></svg></a></span></div><nav aria-label="breadcrumb" class="hidden lg:flex"><ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-content-base sm:gap-2"><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 [&amp;&gt;svg]:text-content-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="inline-flex items-center gap-1.5 font-medium lowercase text-content-light"><a class="font-medium text-content-base transition-colors hover:text-content-strong decoration-content-lighter decoration-dotted underline-offset-4 hover:underline" href="/">Home</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 [&amp;&gt;svg]:text-content-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="inline-flex items-center gap-1.5 font-medium lowercase text-content-light"><a class="font-medium text-content-base transition-colors hover:text-content-strong decoration-content-lighter decoration-dotted underline-offset-4 hover:underline" href="/models">Explore</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 [&amp;&gt;svg]:text-content-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="inline-flex items-center gap-1.5 font-medium lowercase text-content-light">fal-ai/framepack</li></ol></nav></div><div class="ml-auto flex items-center justify-end space-x-2 opacity-80 transition-opacity hover:opacity-100 max-lg:hidden"><a href="https://docs.fal.ai" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Docs</a><a href="https://blog.fal.ai" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Blog</a><a href="/pricing" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Pricing</a><a href="/enterprise" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Enterprise</a><a href="/careers" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Careers</a><a href="/grants" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Research Grants</a></div><div class="flex flex-1 items-center justify-end space-x-2 pr-3 md:pr-0 lg:flex-none"><div class="ms-4 hidden md:flex"></div><a href="/login?returnTo=/models/fal-ai/framepack" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Log-in</a><a href="/login?returnTo=/models/fal-ai/framepack" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] bg-primary text-primary-foreground hover:bg-primary-dark border-primary-dark [&amp;&gt;svg]:text-primary-300 h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5 max-md:hidden">Sign-up</a></div></div></div></div></div><div class="px-0"><main class="flex-grow"><div class="overflow-visible border-b border-stroke-light bg-surface-light"><div class="container mx-auto px-4 py-0"><div class="group flex flex-row py-2 md:py-3 lg:py-4"><div class="flex-1"><div class="mb-4 flex flex-row text-sm lg:hidden"><nav aria-label="breadcrumb"><ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-content-base sm:gap-2"><li class="inline-flex items-center gap-1.5 font-medium lowercase text-content-light"><a class="font-medium text-content-base transition-colors hover:text-content-strong decoration-content-lighter decoration-dotted underline-offset-4 hover:underline flex flex-row items-center space-x-2" href="/models"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left icon-xs"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg><span>Back to Gallery</span></a></li></ol></nav></div><div class="flex flex-col space-y-2"><div class="flex flex-row items-baseline justify-between space-x-4 md:justify-normal"><div class="flex flex-col items-baseline gap-2"><h1 class="sr-only">Framepack Image to Video</h1><div class="flex flex-col items-start space-y-2 text-lg font-light md:flex-row md:items-center md:space-x-2 md:space-y-0 md:text-xl lg:text-2xl"><div class="flex flex-row items-center space-x-2"><span class="truncate text-nowrap font-focal text-lg text-current md:text-3xl md:tracking-wide"><span class="shrink-0 font-normal opacity-60">fal-ai<!-- -->/</span><span class="text-nowrap font-medium">framepack</span></span><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong h-9 w-9 [&amp;&gt;svg]:w-4 [&amp;&gt;svg]:h-4 [&amp;&gt;svg]:text-current opacity-70 hover:opacity-100" type="button" aria-label="Copy" name="copyCode"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-files"><path d="M20 7h-3a2 2 0 0 1-2-2V2"></path><path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"></path><path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"></path></svg></button></div><div class="flex flex-row items-center space-x-2"><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] [&amp;&gt;svg]:h-6 [&amp;&gt;svg]:w-6 bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-10 px-4 w-fit lg:h-10 lg:px-4 lg:text-lg" type="button" id="radix-:Rcklj7rmjkq:" aria-haspopup="menu" aria-expanded="false" data-state="closed">Image-to-Video<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"></path></svg></button><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info icon-sm cursor-pointer text-content-lighter" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:Rkklj7rmjkq:" data-state="closed"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg></div></div><div class="line-clamp-2 max-w-3xl text-sm text-content-light">Framepack is an efficient Image-to-video model that autoregressively generates videos.</div><div class="mt-4 flex flex-row space-x-1"><div class="cursor-default" data-state="closed"><div class="inline-flex items-center rounded border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent px-1.5 py-0.5 text-xs bg-primary-600/20 text-primary-dark dark:text-primary-500 h-5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-atom h-3 w-3"><circle cx="12" cy="12" r="1"></circle><path d="M20.2 20.2c2.04-2.03.02-7.36-4.5-11.9-4.54-4.52-9.87-6.54-11.9-4.5-2.04 2.03-.02 7.36 4.5 11.9 4.54 4.52 9.87 6.54 11.9 4.5Z"></path><path d="M15.7 15.7c4.52-4.54 6.54-9.87 4.5-11.9-2.03-2.04-7.36-.02-11.9 4.5-4.52 4.54-6.54 9.87-4.5 11.9 2.03 2.04 7.36.02 11.9-4.5Z"></path></svg><span class="font-xs ms-1.5 text-nowrap">Inference</span></div></div><div class="cursor-default" data-state="closed"><div class="inline-flex items-center rounded border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent px-1.5 py-0.5 text-xs bg-success-400/30 text-success-800 dark:bg-success-400/20 dark:text-success-500 h-5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-dollar-sign h-3 w-3"><line x1="12" x2="12" y1="2" y2="22"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg><span class="font-xs ms-1.5 text-nowrap">Commercial use</span></div></div></div></div></div></div></div><div class="hidden flex-none flex-row items-start justify-end gap-2 md:flex"><a href="/api/openapi/queue/openapi.json?endpoint_id=fal-ai/framepack" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-json"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 12a1 1 0 0 0-1 1v1a1 1 0 0 1-1 1 1 1 0 0 1 1 1v1a1 1 0 0 0 1 1"></path><path d="M14 18a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1 1 1 0 0 1-1-1v-1a1 1 0 0 0-1-1"></path></svg><span>Schema</span></a></div></div><div class="flex items-end justify-between"><div class="-mb-[2px] font-heading"><div class="flex max-w-[100vw] overflow-x-auto max-md:w-full max-md:justify-stretch"><a class="ring-offset-background focus-visible:ring-ring flex h-10 flex-1 cursor-pointer select-none items-center justify-center rounded-none border-b-2 px-4 font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-transparent transition-all duration-100 border-primary text-content-stronger hover:border-primary hover:text-content-strong" aria-current="page" href="/models/fal-ai/framepack/playground">Playground</a><a class="ring-offset-background focus-visible:ring-ring flex h-10 flex-1 cursor-pointer select-none items-center justify-center rounded-none border-b-2 px-4 font-medium text-content-light focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-transparent transition-all duration-100 hover:text-content-base border-transparent" href="/models/fal-ai/framepack/api">API</a></div></div><div></div></div></div></div><div class="container mx-auto px-4 py-6"><div class="mb-4 flex flex-row"><div class="flex-1"></div><div class="flex flex-row items-center space-x-2"></div></div><div class="grid w-full grid-cols-1 gap-8 lg:grid-cols-12"><div class="space-y-8 lg:col-span-6"><div data-layout="default" class="flex flex-col rounded border border-stroke-light bg-surface group" id="model-form-card"><div class="flex flex-col p-6"><div class="flex flex-row items-start justify-between"><h3 class="font-heading text-lg leading-none tracking-tight text-content-base lg:text-xl">Input</h3><button type="button" role="combobox" aria-controls="radix-:R2b9uj7rmjkq:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex h-10 items-center justify-between rounded bg-surface-alpha font-heading text-content-strong border border-stroke-strong hover:border-stroke-stronger px-3 py-1.5 font-medium transition-colors placeholder:text-content-lighter focus-ring disabled:cursor-not-allowed disabled:opacity-50 [&amp;&gt;span]:line-clamp-1 w-fit"><span style="pointer-events:none"><div class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panels-top-left icon-sm"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M9 21V9"></path></svg><span>Form</span></div></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down icon-xs icon-strong ms-2 stroke-content-strong" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><select aria-hidden="true" tabindex="-1" style="position:absolute;border:0;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;word-wrap:normal"></select></div></div><div class="flex-1 p-6 pt-0 group-data-[layout=compact]:m-0 group-data-[layout=compact]:p-0 group-data-[layout=compact]:rounded-xl group-data-[layout=compact]:border-none pb-0"><div class="space-y-8"><div class="space-y-6"><div class="space-y-6"><div class="form-control space-y-2"><div class="flex flex-row items-center space-x-1"><label class="text-content-strong font-normal font-heading leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="model-input-prompt"><span class="label-text">Prompt</span><span class="text-primary">*</span></label><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info icon-sm cursor-pointer text-content-lighter" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:R259j9uj7rmjkq:" data-state="closed"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg></div><textarea class="flex min-h-[80px] w-full rounded border border-stroke-base bg-surface-alpha px-3 py-2 text-sm text-content placeholder:text-content-lighter focus-ring disabled:cursor-not-allowed disabled:opacity-50 no-scrollbar resize-none" id="model-input-prompt" rows="3" name="prompt" autoComplete="off" spellCheck="false">A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.</textarea></div><div class="form-control space-y-2"><div class="flex flex-row items-center space-x-1"><label class="text-content-strong font-normal font-heading leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="model-input-image_url"><span class="label-text">Image Url</span><span class="text-primary">*</span></label><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info icon-sm cursor-pointer text-content-lighter" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:R269j9uj7rmjkq:" data-state="closed"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg></div><div class="space-y-4" role="presentation" tabindex="0"><div class="flex max-w-full flex-row items-center space-x-2"><input type="text" class="flex h-10 w-full rounded border border-stroke-base bg-surface-alpha px-3 py-2 text-sm font-medium text-content transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-content-lighter focus-ring disabled:cursor-not-allowed disabled:opacity-50 flex-1" placeholder="Add a file or provide an URL" value="https://storage.googleapis.com/falserverless/framepack/framepack.jpg"/><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] [&amp;&gt;svg]:h-6 [&amp;&gt;svg]:w-6 bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-10 px-4 flex-none" type="button">Choose...</button></div><input type="file" class="h-10 w-full rounded border border-stroke-base bg-surface-alpha px-3 py-2 text-sm font-medium text-content transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-content-lighter focus-ring disabled:cursor-not-allowed disabled:opacity-50 hidden" accept="image/jpeg,.jpg,.jpeg,image/png,.png,image/webp,.webp,image/gif,.gif,image/avif,.avif" style="display:none" tabindex="-1"/><p class="text-sm text-content-light"><strong>Hint:</strong> you can drag and drop file(s) here, or provide a base64 encoded data URL <!-- -->Accepted file types: jpg, jpeg, png, webp, gif, avif</p><div data-state="closed" class="space-y-2"><div class="grid grid-cols-3 gap-2 lg:grid-cols-5"><div class="flex aspect-square items-center justify-center relative cursor-pointer overflow-hidden rounded border-2 border-stroke-light hover:border-secondary data-[selected=true]:border-primary data-[selected=true]:p-[2px]" data-selected="true"><div class="pointer-events-none absolute inset-0 m-[2px] flex justify-end bg-black/40"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check m-1 fill-primary stroke-[1.5px] text-primary-300"><circle cx="12" cy="12" r="10"></circle><path d="m9 12 2 2 4-4"></path></svg></div><img src="https://storage.googleapis.com/falserverless/framepack/framepack.jpg" alt="" class="h-full w-full object-cover"/></div></div></div></div></div><div data-state="closed"><div class="flex flex-col space-y-0.5"><div class="flex flex-row items-center space-x-2"><span class="font-heading">Additional Settings</span><div class="h-px flex-1 bg-stroke-light"></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-8 px-2.5 text-sm [&amp;&gt;svg]:h-4 [&amp;&gt;svg]:w-4" type="button"><span>More</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down icon-sm"><path d="m6 9 6 6 6-6"></path></svg></button></div><p class="text-sm text-content-lighter">Customize your input with more control.</p></div><div data-state="closed" id="radix-:Rhj9uj7rmjkq:" hidden=""></div></div></div></div><div><div class="pb-6 "><div class="flex flex-row items-center gap-2"><div class="flex flex-1 flex-row items-center justify-end gap-2"><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] [&amp;&gt;svg]:h-6 [&amp;&gt;svg]:w-6 bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-10 px-4" type="reset" aria-disabled="false" name="reset">Reset</button><a href="/login?returnTo=/models/fal-ai/framepack" class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] [&amp;&gt;svg]:h-6 [&amp;&gt;svg]:w-6 bg-primary text-primary-foreground hover:bg-primary-dark border-primary-dark [&amp;&gt;svg]:text-primary-300 h-10 px-4" name="login">Sign in to run</a></div></div></div></div></div></div></div></div><div class="space-y-8 lg:col-span-6"><div data-layout="default" class="flex flex-col rounded border border-stroke-light bg-surface group scroll-m-8"><div class="flex flex-col p-6"><div class="flex flex-row items-start justify-between"><div class="flex flex-row items-center space-x-2"><h3 class="font-heading text-lg leading-none tracking-tight text-content-base lg:text-xl">Result</h3><div class="inline-flex items-center rounded border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent px-2 py-0.5 text-sm bg-surface-alpha-strong text-content-base">Idle</div></div><button type="button" role="combobox" aria-controls="radix-:R12d9uj7rmjkq:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex h-10 items-center justify-between rounded bg-surface-alpha font-heading text-content-strong border border-stroke-strong hover:border-stroke-stronger px-3 py-1.5 font-medium transition-colors placeholder:text-content-lighter focus-ring disabled:cursor-not-allowed disabled:opacity-50 [&amp;&gt;span]:line-clamp-1 w-fit"><span style="pointer-events:none"><div class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image icon-sm"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><circle cx="9" cy="9" r="2"></circle><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path></svg><span>Preview</span></div></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down icon-xs icon-strong ms-2 stroke-content-strong" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><select aria-hidden="true" tabindex="-1" style="position:absolute;border:0;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;word-wrap:normal"></select></div></div><div class="flex-1 p-6 pt-0 group-data-[layout=compact]:m-0 group-data-[layout=compact]:p-0 group-data-[layout=compact]:rounded-xl group-data-[layout=compact]:border-none"><span class="mt-2 text-sm text-content-light">This generation takes approximately<!-- --> <span class="text-content-strong">3<!-- -->m</span>.</span><div class=""><div class="flex flex-col items-center justify-center"><div class="flex w-full flex-col items-center justify-center space-y-4"><div class="flex flex-col items-center justify-center"><div class="model-result my-6 flex min-h-[300px] w-full flex-col items-center justify-center"><div class="video-player relative w-full"><video autoPlay="" loop="" muted="" src="https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4" controls="" preload="metadata"></video></div></div></div></div></div><div class="hidden [&amp;_pre_*]:break-all"><div class="flex flex-col items-center justify-center w-full flex-1 flex-wrap"><div class="not-prose h-auto max-h-full w-full space-y-2"><div class="group relative rounded bg-surface-alpha font-mono text-sm h-full no-scrollbar max-h-[36rem] min-h-[12rem] w-full overflow-y-auto"><div class="absolute right-0 top-0 p-2 opacity-50 transition-opacity group-hover:opacity-100"><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] border-transparent bg-transparent text-content-base hover:bg-surface-alpha-strong hover:text-content-strong data-[state=open]:bg-surface-alpha-strong h-9 w-9 [&amp;&gt;svg]:w-4 [&amp;&gt;svg]:h-4 [&amp;&gt;svg]:text-current opacity-70 hover:opacity-100" type="button" aria-label="Copy" name="copyCode"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-files"><path d="M20 7h-3a2 2 0 0 1-2-2V2"></path><path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"></path><path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"></path></svg></button></div><pre text="{
  &quot;video&quot;: {
    &quot;url&quot;: &quot;https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4&quot;
  }
}" style="background:transparent;color:hsl(220, 14%, 71%);text-shadow:0 1px rgba(0, 0, 0, 0.3);font-family:&quot;Fira Code&quot;, &quot;Fira Mono&quot;, Menlo, Consolas, &quot;DejaVu Sans Mono&quot;, monospace;direction:ltr;text-align:left;white-space:pre-wrap;word-spacing:normal;word-break:normal;line-height:1rem;-moz-tab-size:2;-o-tab-size:2;tab-size:2;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none;padding:1rem;margin:0 auto;overflow:scroll;border-radius:0.3em;overflow-wrap:break-word;max-height:100%"><code class="bg-transparent leading-normal whitespace-pre-wrap" style="white-space:pre-wrap"><span style="background-color:transparent"><span class="token" style="color:hsl(220, 14%, 71%)">{</span><span>
</span></span><span style="background-color:transparent"><span>  </span><span class="token" style="color:hsl(355, 65%, 65%)">&quot;video&quot;</span><span class="token" style="color:hsl(207, 82%, 66%)">:</span><span> </span><span class="token" style="color:hsl(220, 14%, 71%)">{</span><span>
</span></span><span style="background-color:transparent"><span>    </span><span class="token" style="color:hsl(355, 65%, 65%)">&quot;url&quot;</span><span class="token" style="color:hsl(207, 82%, 66%)">:</span><span> </span><span class="token" style="color:hsl(95, 38%, 62%)">&quot;https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4&quot;</span><span>
</span></span><span style="background-color:transparent"><span>  </span><span class="token" style="color:hsl(220, 14%, 71%)">}</span><span>
</span></span><span style="background-color:transparent"><span></span><span class="token" style="color:hsl(220, 14%, 71%)">}</span></span></code></pre></div></div></div></div></div></div><div class="flex items-center p-6 pt-4 text-sm text-content-light"><div class="flex flex-col items-start space-y-3"><div class="space-y-3"><p>Your request<!-- --> <!-- --> <!-- -->will cost<!-- --> <span class="font-semibold text-content-strong">$<!-- -->0.0333</span> <!-- -->per <!-- --> <!-- -->second<!-- -->.</p></div></div></div></div><div data-layout="default" class="flex flex-col rounded border border-stroke-light bg-surface group"><div class="p-6 flex cursor-pointer select-none flex-row items-center justify-between"><h3 class="font-heading text-lg leading-none tracking-tight text-content-base lg:text-xl">Logs</h3><div class="flex flex-row items-center space-x-2"><button class="inline-flex items-center justify-center whitespace-nowrap rounded border font-heading font-medium transition-all disabled:pointer-events-none disabled:opacity-50 focus-ring gap-1.5 [&amp;&gt;svg]:stroke-[1.5] bg-transparent text-content-strong border-stroke-strong hover:text-content-stronger hover:border-stroke-stronger data-[state=open]:bg-surface-alpha [&amp;&gt;svg]:text-content-lighter h-9 px-3 text-sm [&amp;&gt;svg]:h-5 [&amp;&gt;svg]:w-5">Show<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down icon-sm"><path d="m6 9 6 6 6-6"></path></svg></button></div></div></div></div></div></div></main></div></div><div class="footer-container container-full mx-auto mt-10 w-full bg-brand-sky px-4 text-content-strong lg:mt-20"><footer class="flex min-h-[424px] w-full flex-col p-4 font-focal md:flex-row"><div class="mb-6 mr-auto flex w-full max-w-screen-lg flex-col justify-between text-sm"><div class="mb-6 mr-auto grid w-full grid-cols-2 md:grid-cols-4"><div class="mb-6 flex w-full flex-col space-y-2 md:w-auto"><h4 class="mb-5 font-medium">Learn More</h4><a href="https://status.fal.ai">Status</a><a href="https://docs.fal.ai" target="_blank">Documentation</a><a href="/pricing">Pricing</a><a href="/enterprise">Enterprise</a><a href="/grants">Grants</a><a href="/about">About Us</a><a target="_blank" rel="noopener noreferrer" href="/careers">Careers</a><a target="_blank" rel="noopener noreferrer" href="https://blog.fal.ai">Blog</a><a href="/cdn-cgi/l/email-protection#6e1d1b1e1e011c1a2e080f02400f07">Get in touch</a></div><div class="mb-6 flex w-full flex-col space-y-2 md:w-auto"><span class="mb-5 font-medium">Models</span><a href="/models/fal-ai/aura-flow">AuraFlow</a><a href="/models/fal-ai/flux/schnell">Flux.1 [schnell]</a><a href="/models/fal-ai/flux/dev">Flux.1 [dev]</a><a href="/models/fal-ai/flux-realism">Flux Realism LoRA</a><a href="/models/fal-ai/flux-lora">Flux LoRA</a><a href="/models">Explore More</a></div><div class="mb-6 flex w-full flex-col space-y-2 md:w-auto"><h4 class="mb-5 font-medium">Playgrounds</h4><a href="/models/fal-ai/flux-lora-fast-training">Training</a><a href="/workflows">Workflows</a><a href="/demos">Demos</a></div><div class="mb-6 flex w-full flex-col space-y-2 md:w-auto"><h4 class="mb-5 font-medium">Socials</h4><a href="https://discord.gg/fal-ai" target="_blank">Discord</a><a href="https://github.com/fal-ai" target="_blank">GitHub</a><a href="https://twitter.com/fal" target="_blank">Twitter</a><a href="https://www.linkedin.com/company/features-and-labels/" target="_blank">Linkedin</a></div></div><div class="my-10 md:mb-0 md:mt-20">features and labels, 2025. All Rights Reserved.<!-- --> <a target="_blank" href="/terms.html" class="underline hover:text-content-stronger">Terms of Service</a> <!-- -->and<!-- --> <a target="_blank" href="/privacy.html" class="underline hover:text-content-stronger">Privacy Policy</a></div></div><svg viewBox="0 0 120 48" fill="none" xmlns="http://www.w3.org/2000/svg" style="fill:currentColor" class="ml-auto h-full self-end fill-black opacity-30 dark:fill-black md:max-h-[110px] lg:max-h-[254px]"><path d="M120 6.46345V41.6633C120 47.2476 119.937 47.4986 119.248 47.4986H110.724C110.034 47.4986 109.909 47.2476 109.909 41.6633V6.46345C109.909 0.**********.034 0.**********.724 0.628174H119.248C119.937 0.********** 0.********** 6.46345Z"></path><path d="M96.0997 27.0431V25.7255C96.0997 22.5882 94.6581 21.3333 92.1509 21.3333C89.7065 21.3333 88.3902 22.651 88.0142 24.9725C87.8888 25.6627 87.9515 26.2274 87.5754 26.2274H79.1138C78.6124 26.2274 78.6124 26.1019 78.6124 25.6627C78.6124 22.3372 81.7463 15.4353 92.6524 15.4353C100.362 15.4353 105.878 18.5098 105.878 27.0431V39.5921C105.878 42.4783 107.445 46.5568 107.445 47.1215C107.445 47.3724 107.257 47.4979 107.068 47.4979H97.6667C97.2279 47.4979 97.1652 47.247 96.8518 45.6783L96.6011 44.4862C96.4131 43.545 96.2877 43.2313 95.9117 43.2313C95.4102 43.2313 95.0968 44.3607 93.7179 45.6783C92.2136 47.0587 90.3959 47.9999 87.262 47.9999C82.185 47.9999 77.6095 45.1136 77.6095 38.8391C77.6095 31.8117 83.0625 28.8 91.1481 28.4862C95.3476 28.298 96.0997 28.7372 96.0997 27.0431ZM96.0997 36.1411V34.8862C96.0997 33.6313 95.7863 33.2548 94.7208 33.3176L92.4017 33.4431C89.4558 33.6313 87.7008 35.1372 87.7008 37.9607C87.7008 40.7215 89.205 42.1019 91.3988 42.1019C93.8433 42.1019 96.0997 39.8431 96.0997 36.1411Z"></path><path d="M60.5509 23.6548C60.5509 22.4627 60.0495 22.3372 57.7304 22.3372H56.6021C56.1634 22.3372 56.1007 22.0234 56.1007 19.4509V19.0117C56.1007 16.4392 56.1634 16.1882 56.6021 16.1882H57.9184C60.2375 16.1882 60.5509 15.9999 60.5509 14.8705V10.9804C60.5509 3.6392 64.2489 0 71.3943 0C74.2148 0 76.1579 0.439212 76.4713 0.627448C76.5966 0.752936 76.5966 0.941172 76.5966 3.26273V4.07842C76.5966 6.46272 76.5966 7.02742 76.4086 7.02742C76.2206 7.02742 75.3431 6.46272 73.8388 6.46272C71.8331 6.46272 70.5795 7.46664 70.5795 10.9804V14.8705C70.5795 15.9999 71.0182 16.1882 73.8388 16.1882H76.6593C77.2234 16.1882 77.2234 16.4392 77.2234 19.0117V19.4509C77.2234 22.0234 77.1607 22.3372 76.722 22.3372H73.8388C71.0182 22.3372 70.5795 22.4627 70.5795 23.6548V41.6626C70.5795 47.2469 70.3914 47.4979 69.89 47.4979H61.1777C60.6762 47.4979 60.5509 47.2469 60.5509 41.6626V23.6548Z"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M30.1574 0.740479C30.9676 0.740479 31.6169 1.39923 31.6944 2.20567C32.3853 9.39891 38.1102 15.1234 45.3039 15.8142C46.1104 15.8917 46.7692 16.541 46.7692 17.3511V30.8959C46.7692 31.706 46.1104 32.3553 45.3039 32.4328C38.1102 33.1236 32.3853 38.8481 31.6944 46.0414C31.6169 46.8478 30.9676 47.5065 30.1574 47.5065H16.6118C15.8016 47.5065 15.1523 46.8478 15.0748 46.0414C14.384 38.8481 8.65901 33.1236 1.46528 32.4328C0.658799 32.3553 0 31.706 0 30.8959V17.3511C0 16.541 0.658803 15.8917 1.46529 15.8142C8.65902 15.1234 14.384 9.39891 15.0748 2.20567C15.1523 1.39923 15.8016 0.740479 16.6118 0.740479H30.1574ZM9.39037 24.0839C9.39037 31.865 15.6915 38.1728 23.4644 38.1728C31.2373 38.1728 37.5385 31.865 37.5385 24.0839C37.5385 16.3028 31.2373 9.99498 23.4644 9.99498C15.6915 9.99498 9.39037 16.3028 9.39037 24.0839Z"></path></svg></footer></div><div class="footer-bar flex h-2 w-full justify-end"><div class="h-full w-[5%] bg-[#AB77FF]"></div><div class="h-full w-[40%] bg-[#99EDFF]"></div><div class="h-full w-[7%] bg-[#AB77FF]"></div><div class="h-full w-[25%] bg-black"></div><div class="h-full w-[10%] bg-[#ADFF00]"></div></div></div></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[460px] z-[60]"></ol></div><script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="/_next/static/chunks/webpack-3c607203c149e2d5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/086688bf406e608f-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/media/0afc9fe188a8dace-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n3:HL[\"/_next/static/media/36a7570408bc4d8b-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n4:HL[\"/_next/static/media/495bfb79d5335391-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n5:HL[\"/_next/static/media/499d477a1bd9bbc9-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n6:HL[\"/_next/static/media/7e69aca6b073fa64-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n7:HL[\"/_next/static/media/d8c14dc5fcaf3a63-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n8:HL[\"/_next/static/media/e4ab94819b1ff12b-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n9:HL[\"/_next/static/media/f20b81b98aac358f-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\na:HL[\"/_next/static/css/50b6d35d7a56d22a.css\",\"style\"]\nb:HL[\"/_next/static/css/18125fdf0299a978.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"c:I[12846,[],\"\"]\n10:I[4707,[],\"\"]\n11:I[36423,[],\"\"]\n12:I[62315,[\"35878\",\"static/chunks/9da6db1e-c3308e728af630be.js\",\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"4074\",\"static/chunks/4074-82e275ff6186196f.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"63185\",\"static/chunks/app/layout-2dff62644a11bb7d.js\"],\"NuqsAdapter\"]\n14:I[88291,[\"35878\",\"static/chunks/9da6db1e-c3308e728af630be.js\",\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"4074\",\"static/chunks/4074-82e275ff6186196f.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"63185\",\"static/chunks/app/layout-2dff62644a11bb7d.js\"],\"Analytics\"]\n16:I[61060,[],\"\"]\n17:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$Lc\",null,{\"buildId\":\"v7ty5K0wKEi7D9LYauFoa\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"models\",\"fal-ai\",\"framepack\"],\"initialTree\":[\"\",{\"children\":[\"models\",{\"children\":[[\"endpointId\",\"fal-ai%2Fframepack\",\"d\"],{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"models\",{\"children\":[[\"endpointId\",\"fal-ai%2Fframepack\",\"d\"],{\"children\":[\"__PAGE__\",{},[[\"$Ld\",\"$Le\",null],null],null]},[[null,\"$Lf\"],null],null]},[null,[\"$\",\"$L10\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"models\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L11\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/50b6d35d7a56d22a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/18125fdf0299a978.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__variable_5e98b6 __variable_afcfa0 __variable_f0d962 __variable_512732\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"href\":\"/favicon.png\",\"rel\":\"shortcut icon\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",null,{\"href\":\"/apple-touch-icon.png\",\"rel\":\"apple-touch-icon\"}],[\"$\",\"link\",null,{\"rel\":\"sitemap\",\"type\":\"application/xml\",\"title\":\"fal.ai sitemap\",\"href\":\"/sitemap.xml\"}],[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]}],[\"$\",\"body\",null,{\"suppressHydrationWarning\":true,\"children\":[[[\"$\",\"script\",null,{\"async\":true,\"src\":\"https://www.googletagmanager.com/gtag/js?id=G-HRNLRXZ833\"}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"window.dataLayer = window.dataLayer || [];\\nfunction gtag(){dataLayer.push(arguments);}\\ngtag('js', new Date());\\n\\ngtag('config', 'G-HRNLRXZ833');\"}}]],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"!function(t){var k=\\\"ko\\\",i=(window.globalKoalaKey=window.globalKoalaKey||k);if(window[i])return;var ko=(window[i]=[]);[\\\"identify\\\",\\\"track\\\",\\\"removeListeners\\\",\\\"on\\\",\\\"off\\\",\\\"qualify\\\",\\\"ready\\\"].forEach(function(t){ko[t]=function(){var n=[].slice.call(arguments);return n.unshift(t),ko.push(n),ko}});var n=document.createElement(\\\"script\\\");n.async=!0,n.setAttribute(\\\"src\\\",\\\"https://cdn.getkoala.com/v1/pk_f63728be455c83e0e4bf62a608599ea7eb65/sdk.js\\\"),(document.body || document.head).appendChild(n)}();\"}}],[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],[\"$\",\"$L14\",null,{}]]}]]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L15\"],\"globalErrorComponent\":\"$16\",\"missingSlots\":\"$W17\"}]\n"])</script><script>self.__next_f.push([1,"18:I[70683,[\"35878\",\"static/chunks/9da6db1e-c3308e728af630be.js\",\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"4074\",\"static/chunks/4074-82e275ff6186196f.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"63185\",\"static/chunks/app/layout-2dff62644a11bb7d.js\"],\"UserAuthProvider\"]\n19:I[862,[\"35878\",\"static/chunks/9da6db1e-c3308e728af630be.js\",\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"4074\",\"static/chunks/4074-82e275ff6186196f.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"63185\",\"static/chunks/app/layout-2dff62644a11bb7d.js\"],\"default\"]\n1a:I[10376,[\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"97601\",\"static/chunks/app/error-2b45736f961575e3.js\"],\"default\"]\n1b:I[75292,[\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"69902\",\"static/chunks/69902-37f48df72b80a01b.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"76798\",\"static/chunks/76798-45c36fef3e62663c.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"28119\",\"static/chunks/28119-89b8f055927fcbad.js\",\"29160\",\"static/chunks/app/not-found-a2d64b30801b4a5e.js\"],\"default\"]\n13:[\"$\",\"$L18\",null,{\"user\":null,\"children\":[\"$\",\"$L19\",null,{\"children"])</script><script>self.__next_f.push([1,"\":[\"$\",\"$L10\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$1a\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L11\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"$L1b\",null,{}],\"notFoundStyles\":[]}]}]}]\n"])</script><script>self.__next_f.push([1,"15:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"Framepack | Image to Video | fal.ai\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\"}],[\"$\",\"meta\",\"4\",{\"name\":\"robots\",\"content\":\"index\"}],[\"$\",\"meta\",\"5\",{\"name\":\"application/ld+json\",\"content\":\"{\\\"@context\\\":\\\"https://schema.org/\\\",\\\"@type\\\":\\\"WebAPI\\\",\\\"identifier\\\":\\\"fal-ai/framepack\\\",\\\"url\\\":\\\"https://fal.run/fal-ai/framepack\\\",\\\"name\\\":\\\"Framepack API served by fal.ai\\\",\\\"description\\\":\\\"Framepack is an efficient Image-to-video model that autoregressively generates videos.. Ready-to-use REST inference API, best in class performance, no coldstarts, fair pricing.\\\",\\\"documentation\\\":\\\"https://fal.ai/models/fal-ai/framepack/api\\\",\\\"termsOfService\\\":\\\"https://fal.ai/terms\\\",\\\"provider\\\":{\\\"@type\\\":\\\"Organization\\\",\\\"name\\\":\\\"fal.ai Inc.\\\",\\\"url\\\":\\\"https://fal.ai\\\"}}\"}],[\"$\",\"link\",\"6\",{\"rel\":\"canonical\",\"href\":\"https://fal.ai/models/fal-ai/framepack\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:title\",\"content\":\"Framepack | Image to Video | fal.ai\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:description\",\"content\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:url\",\"content\":\"https://fal.ai/models/fal-ai/framepack\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image\",\"content\":\"https://fal.ai/api/models/thumbnail/fal-ai/framepack\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Framepack | Image to Video | fal.ai\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:creator\",\"content\":\"@fal\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:title\",\"content\":\"Framepack | Image to Video | fal.ai\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:description\",\"content\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:image\",\"content\":\"https://fal.ai/api/models/thumbnail/fal-ai/framepack\"}],[\"$\",\"meta\",\"18\",{\"name\":\"next-size-adjust\"}]]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"1c:I[7778,[\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"69902\",\"static/chunks/69902-37f48df72b80a01b.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"76798\",\"static/chunks/76798-45c36fef3e62663c.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"71632\",\"static/chunks/71632-69addefea7ce37b8.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"28119\",\"static/chunks/28119-89b8f055927fcbad.js\",\"26470\",\"static/chunks/26470-6c5f3171f6709917.js\",\"89105\",\"static/chunks/89105-f233706d7cccd674.js\",\"88447\",\"static/chunks/88447-702256d80b07eb23.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"51755\",\"static/chunks/51755-09ce4f3b30cceebd.js\",\"92813\",\"static/chunks/92813-a57920cff6ad1371.js\",\"62266\",\"static/chunks/62266-393fbff85279c7d7.js\",\"85281\",\"static/chunks/85281-6654c5be68c11c67.js\",\"46516\",\"static/chunks/46516-70a7cb42b896b142.js\",\"12172\",\"static/chunks/12172-e0a6d2f5d47e9d0e.js\",\"61482\",\"static/chunks/61482-8483b2021864fc02.js\",\"18642\",\"static/chunks/18642-3631ad70a3aeac91.js\",\"73892\",\"static/chunks/73892-eb7b6ccbc0fd6e5e.js\",\"31229\",\"static/chunks/31229-42e6c705eb176915.js\",\"13590\",\"static/chunks/13590-f69849ac25e543d3.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"71333\",\"static/chunks/71333-5f42f848925af109.js\",\"98489\",\"static/chunks/98489-a17f09fdd330277e.js\",\"48945\",\"static/chunks/app/models/%5BendpointId%5D/layout-93cf258556dc53bf.js\"],\"ModelPagesClientLayout\"]\n22:I[42546,[\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"69902\",\"static/chunks/69902-37f48df72b80a01b.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"76798\",\"static/chunks/76798-45c36fef3e62663c.js\",\"82957\",\"static/chu"])</script><script>self.__next_f.push([1,"nks/82957-aef5ae7f4ee19098.js\",\"71632\",\"static/chunks/71632-69addefea7ce37b8.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"28119\",\"static/chunks/28119-89b8f055927fcbad.js\",\"26470\",\"static/chunks/26470-6c5f3171f6709917.js\",\"89105\",\"static/chunks/89105-f233706d7cccd674.js\",\"88447\",\"static/chunks/88447-702256d80b07eb23.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"51755\",\"static/chunks/51755-09ce4f3b30cceebd.js\",\"92813\",\"static/chunks/92813-a57920cff6ad1371.js\",\"62266\",\"static/chunks/62266-393fbff85279c7d7.js\",\"85281\",\"static/chunks/85281-6654c5be68c11c67.js\",\"46516\",\"static/chunks/46516-70a7cb42b896b142.js\",\"12172\",\"static/chunks/12172-e0a6d2f5d47e9d0e.js\",\"61482\",\"static/chunks/61482-8483b2021864fc02.js\",\"18642\",\"static/chunks/18642-3631ad70a3aeac91.js\",\"73892\",\"static/chunks/73892-eb7b6ccbc0fd6e5e.js\",\"31229\",\"static/chunks/31229-42e6c705eb176915.js\",\"13590\",\"static/chunks/13590-f69849ac25e543d3.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"71333\",\"static/chunks/71333-5f42f848925af109.js\",\"98489\",\"static/chunks/98489-a17f09fdd330277e.js\",\"48945\",\"static/chunks/app/models/%5BendpointId%5D/layout-93cf258556dc53bf.js\"],\"ModelInfoProvider\"]\n23:I[26263,[\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"69902\",\"static/chunks/69902-37f48df72b80a01b.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"76798\",\"static/chunks/76798-45c36fef3e62663c.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"71632\",\"static/chunks/71632-69addefea7ce37b8.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"28119\",\"static/chunks/28119-89b8f055927fcbad.js\",\"26470\",\"static/chunks/26470-6c5f3171f6709917.js\",\"89105\",\"static/chunks/89105-f233706d7cccd674.js\",\"88447\",\"static/chunks/88447-702256d80b07eb23.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"51755\",\"static/chunks/51755-09ce4f3b30cceebd.js\",\"928"])</script><script>self.__next_f.push([1,"13\",\"static/chunks/92813-a57920cff6ad1371.js\",\"62266\",\"static/chunks/62266-393fbff85279c7d7.js\",\"85281\",\"static/chunks/85281-6654c5be68c11c67.js\",\"46516\",\"static/chunks/46516-70a7cb42b896b142.js\",\"12172\",\"static/chunks/12172-e0a6d2f5d47e9d0e.js\",\"61482\",\"static/chunks/61482-8483b2021864fc02.js\",\"18642\",\"static/chunks/18642-3631ad70a3aeac91.js\",\"73892\",\"static/chunks/73892-eb7b6ccbc0fd6e5e.js\",\"31229\",\"static/chunks/31229-42e6c705eb176915.js\",\"13590\",\"static/chunks/13590-f69849ac25e543d3.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"71333\",\"static/chunks/71333-5f42f848925af109.js\",\"98489\",\"static/chunks/98489-a17f09fdd330277e.js\",\"48945\",\"static/chunks/app/models/%5BendpointId%5D/layout-93cf258556dc53bf.js\"],\"default\"]\n24:I[7778,[\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"69902\",\"static/chunks/69902-37f48df72b80a01b.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"76798\",\"static/chunks/76798-45c36fef3e62663c.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"71632\",\"static/chunks/71632-69addefea7ce37b8.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"28119\",\"static/chunks/28119-89b8f055927fcbad.js\",\"26470\",\"static/chunks/26470-6c5f3171f6709917.js\",\"89105\",\"static/chunks/89105-f233706d7cccd674.js\",\"88447\",\"static/chunks/88447-702256d80b07eb23.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"51755\",\"static/chunks/51755-09ce4f3b30cceebd.js\",\"92813\",\"static/chunks/92813-a57920cff6ad1371.js\",\"62266\",\"static/chunks/62266-393fbff85279c7d7.js\",\"85281\",\"static/chunks/85281-6654c5be68c11c67.js\",\"46516\",\"static/chunks/46516-70a7cb42b896b142.js\",\"12172\",\"static/chunks/12172-e0a6d2f5d47e9d0e.js\",\"61482\",\"static/chunks/61482-8483b2021864fc02.js\",\"18642\",\"static/chunks/18642-3631ad70a3aeac91.js\",\"73892\",\"static/chunks/73892-eb7b6ccbc0fd6e5e.js\",\"31229\",\"static/chunks/31229-42e6c705eb176915.js\","])</script><script>self.__next_f.push([1,"\"13590\",\"static/chunks/13590-f69849ac25e543d3.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"71333\",\"static/chunks/71333-5f42f848925af109.js\",\"98489\",\"static/chunks/98489-a17f09fdd330277e.js\",\"48945\",\"static/chunks/app/models/%5BendpointId%5D/layout-93cf258556dc53bf.js\"],\"ModelSubheader\"]\n1e:[\"image to video\",\"motion\"]\n1f:{\"key\":\"framepack\",\"label\":\"Image-to-Video\"}\n20:[]\n21:[]\n1d:{\"id\":\"fal-ai/framepack\",\"modelId\":\"d10ea3nqn7dbfsbcfb3g\",\"isFavorited\":false,\"title\":\"Framepack\",\"category\":\"image-to-video\",\"tags\":\"$1e\",\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://v3.fal.media/files/koala/dUfFd9Z7aSX06gL2_qXn0_image.webp\",\"modelUrl\":\"https://fal.run/fal-ai/framepack\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-04-17T17:10:10.829Z\",\"creditsRequired\":2,\"group\":\"$1f\",\"machineType\":null,\"examples\":\"$20\",\"durationEstimate\":3,\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":\"$21\"}\n26:[\"us-central\",\"us-west\",\"us-east\",\"eu-north\"]\n30:{\"$ref\":\"#/components/schemas/FramePackF1Response\"}\n2f:{\"schema\":\"$30\"}\n2e:{\"application/json\":\"$2f\"}\n2d:{\"content\":\"$2e\",\"description\":\"Successful Response\"}\n34:{\"$ref\":\"#/components/schemas/HTTPValidationError\"}\n33:{\"schema\":\"$34\"}\n32:{\"application/json\":\"$33\"}\n31:{\"content\":\"$32\",\"description\":\"Validation Error\"}\n2c:{\"200\":\"$2d\",\"422\":\"$31\"}\n38:{\"$ref\":\"#/components/schemas/FramePackF1Request\"}\n37:{\"schema\":\"$38\"}\n36:{\"application/json\":\"$37\"}\n35:{\"content\":\"$36\",\"required\":true}\n2b:{\"responses\":\"$2c\",\"requestBody\":\"$35\",\"summary\":\"Generate Video F1 Endpoint\",\"operationId\":\"generate_video_f1_endpoint_f1_post\"}\n2a:{\"post\":\"$2b\"}\n3f:{\"$ref\":\"#/components/schemas/FramePackResponse\"}\n3e:{\"schema\":\"$3f\"}\n3d:{\"application/json\":\"$3e\"}\n3c:{\"content\":\"$3d\",\"description\":\"Successful Response\"}\n43:{\"$ref\":\"#/components/schemas/HTTPValida"])</script><script>self.__next_f.push([1,"tionError\"}\n42:{\"schema\":\"$43\"}\n41:{\"application/json\":\"$42\"}\n40:{\"content\":\"$41\",\"description\":\"Validation Error\"}\n3b:{\"200\":\"$3c\",\"422\":\"$40\"}\n47:{\"$ref\":\"#/components/schemas/FramePackRequest\"}\n46:{\"schema\":\"$47\"}\n45:{\"application/json\":\"$46\"}\n44:{\"content\":\"$45\",\"required\":true}\n3a:{\"responses\":\"$3b\",\"requestBody\":\"$44\",\"summary\":\"Generate Video Regular Endpoint\",\"operationId\":\"generate_video_regular_endpoint__post\"}\n39:{\"post\":\"$3a\"}\n4e:{\"$ref\":\"#/components/schemas/FramePackFLF2VResponse\"}\n4d:{\"schema\":\"$4e\"}\n4c:{\"application/json\":\"$4d\"}\n4b:{\"content\":\"$4c\",\"description\":\"Successful Response\"}\n52:{\"$ref\":\"#/components/schemas/HTTPValidationError\"}\n51:{\"schema\":\"$52\"}\n50:{\"application/json\":\"$51\"}\n4f:{\"content\":\"$50\",\"description\":\"Validation Error\"}\n4a:{\"200\":\"$4b\",\"422\":\"$4f\"}\n56:{\"$ref\":\"#/components/schemas/FramePackF2LFRequest\"}\n55:{\"schema\":\"$56\"}\n54:{\"application/json\":\"$55\"}\n53:{\"content\":\"$54\",\"required\":true}\n49:{\"responses\":\"$4a\",\"requestBody\":\"$53\",\"summary\":\"Generate Video Flf2V Endpoint\",\"operationId\":\"generate_video_flf2v_endpoint_flf2v_post\"}\n48:{\"post\":\"$49\"}\n5d:{}\n5c:{\"schema\":\"$5d\"}\n5b:{\"application/json\":\"$5c\"}\n5a:{\"content\":\"$5b\",\"description\":\"Successful Response\"}\n59:{\"200\":\"$5a\"}\n58:{\"responses\":\"$59\",\"summary\":\"Health\",\"operationId\":\"health_health_get\"}\n57:{\"get\":\"$58\"}\n29:{\"/f1\":\"$2a\",\"/\":\"$39\",\"/flf2v\":\"$48\",\"/health\":\"$57\"}\n5e:{\"version\":\"0.1.0\",\"title\":\"FastAPI\"}\n63:{\"title\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\"}\n66:{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}\n65:[\"$66\"]\n64:{\"examples\":\"$65\",\"$ref\":\"#/components/schemas/File\"}\n62:{\"seed\":\"$63\",\"video\":\"$64\"}\n67:[\"video\",\"seed\"]\n68:[\"video\",\"seed\"]\n61:{\"title\":\"FramePackResponse\",\"type\":\"object\",\"properties\":\"$62\",\"x-fal-order-properties\":\"$67\",\"required\":\"$68\"}\n6b:{\"title\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\"}\n6e:{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60"])</script><script>self.__next_f.push([1,"dcWEv9LVX_output_video.mp4\"}\n6d:[\"$6e\"]\n6c:{\"examples\":\"$6d\",\"$ref\":\"#/components/schemas/File\"}\n6a:{\"seed\":\"$6b\",\"video\":\"$6c\"}\n6f:[\"video\",\"seed\"]\n70:[\"video\",\"seed\"]\n69:{\"title\":\"FramePackF1Response\",\"type\":\"object\",\"properties\":\"$6a\",\"x-fal-order-properties\":\"$6f\",\"required\":\"$70\"}\n74:[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"]\n73:{\"examples\":\"$74\",\"title\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\"}\n76:[\"16:9\",\"9:16\"]\n75:{\"enum\":\"$76\",\"title\":\"Aspect Ratio (W:H)\",\"type\":\"string\",\"description\":\"The aspect ratio of the video to generate.\",\"default\":\"16:9\"}\n78:[\"720p\",\"480p\"]\n77:{\"enum\":\"$78\",\"title\":\"Resolution\",\"type\":\"string\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.\",\"default\":\"480p\"}\n79:{\"minimum\":30,\"title\":\"Number of Frames\",\"type\":\"integer\",\"maximum\":900,\"description\":\"The number of frames to generate.\",\"default\":180}\n7b:[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"]\n7a:{\"examples\":\"$7b\",\"title\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\"}\n7c:{\"minimum\":0,\"title\":\"Guidance Scale\",\"type\":\"number\",\"maximum\":32,\"description\":\"Guidance scale for the generation.\",\"default\":10}\n7f:{\"type\":\"integer\"}\n80:{\"type\":\"null\"}\n7e:[\"$7f\",\"$80\"]\n7d:{\"anyOf\":\"$7e\",\"title\":\"Seed\",\"description\":\"The seed to use for generating the video.\"}\n82:[true]\n81:{\"examples\":\"$82\",\"title\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"default\":false}\n84:[\"Ugly, blurry distorted, bad quality\"]\n83:{\"examples\":\"$84\",\"title\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation.\",\"default\":\"\"}\n85:{\"minimum\":0,\"title\":\"CFG Scale\",\"type\":\"number\",\"maximum\":7,\"description\":\"Classifier-Free Guidance scale for the generation.\",\"default\":1}\n72:{\"prompt\":\"$7"])</script><script>self.__next_f.push([1,"3\",\"aspect_ratio\":\"$75\",\"resolution\":\"$77\",\"num_frames\":\"$79\",\"image_url\":\"$7a\",\"guidance_scale\":\"$7c\",\"seed\":\"$7d\",\"enable_safety_checker\":\"$81\",\"negative_prompt\":\"$83\",\"cfg_scale\":\"$85\"}\n86:[\"prompt\",\"negative_prompt\",\"image_url\",\"seed\",\"aspect_ratio\",\"resolution\",\"cfg_scale\",\"guidance_scale\",\"num_frames\",\"enable_safety_checker\"]\n87:[\"prompt\",\"image_url\"]\n71:{\"title\":\"FramePackF1Request\",\"type\":\"object\",\"properties\":\"$72\",\"x-fal-order-properties\":\"$86\",\"required\":\"$87\"}\n8a:{\"title\":\"Error Type\",\"type\":\"string\"}\n8b:{\"title\":\"Message\",\"type\":\"string\"}\n8f:{\"type\":\"string\"}\n90:{\"type\":\"integer\"}\n8e:[\"$8f\",\"$90\"]\n8d:{\"anyOf\":\"$8e\"}\n8c:{\"title\":\"Location\",\"type\":\"array\",\"items\":\"$8d\"}\n89:{\"type\":\"$8a\",\"msg\":\"$8b\",\"loc\":\"$8c\"}\n91:[\"loc\",\"msg\",\"type\"]\n92:[\"loc\",\"msg\",\"type\"]\n88:{\"title\":\"ValidationError\",\"type\":\"object\",\"properties\":\"$89\",\"x-fal-order-properties\":\"$91\",\"required\":\"$92\"}\n96:[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"]\n95:{\"examples\":\"$96\",\"title\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\"}\n98:[\"16:9\",\"9:16\"]\n97:{\"enum\":\"$98\",\"title\":\"Aspect Ratio (W:H)\",\"type\":\"string\",\"description\":\"The aspect ratio of the video to generate.\",\"default\":\"16:9\"}\n9a:[\"720p\",\"480p\"]\n99:{\"enum\":\"$9a\",\"title\":\"Resolution\",\"type\":\"string\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.\",\"default\":\"480p\"}\n9b:{\"minimum\":30,\"title\":\"Number of Frames\",\"type\":\"integer\",\"maximum\":900,\"description\":\"The number of frames to generate.\",\"default\":180}\n9d:[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"]\n9c:{\"examples\":\"$9d\",\"title\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\"}\n9e:{\"minimum\":0,\"title\":\"Guidance Scale\",\"type\":\"number\",\"maximum\":32,\"description\":\"Guidance scale for the generation.\",\"default\":10}\na1:{\"type\":\"integer\"}\na2:{\"type\":\"null\"}\na"])</script><script>self.__next_f.push([1,"0:[\"$a1\",\"$a2\"]\n9f:{\"anyOf\":\"$a0\",\"title\":\"Seed\",\"description\":\"The seed to use for generating the video.\"}\na4:[true]\na3:{\"examples\":\"$a4\",\"title\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"default\":false}\na6:[\"Ugly, blurry distorted, bad quality\"]\na5:{\"examples\":\"$a6\",\"title\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation.\",\"default\":\"\"}\na7:{\"minimum\":0,\"title\":\"CFG Scale\",\"type\":\"number\",\"maximum\":7,\"description\":\"Classifier-Free Guidance scale for the generation.\",\"default\":1}\n94:{\"prompt\":\"$95\",\"aspect_ratio\":\"$97\",\"resolution\":\"$99\",\"num_frames\":\"$9b\",\"image_url\":\"$9c\",\"guidance_scale\":\"$9e\",\"seed\":\"$9f\",\"enable_safety_checker\":\"$a3\",\"negative_prompt\":\"$a5\",\"cfg_scale\":\"$a7\"}\na8:[\"prompt\",\"negative_prompt\",\"image_url\",\"seed\",\"aspect_ratio\",\"resolution\",\"cfg_scale\",\"guidance_scale\",\"num_frames\",\"enable_safety_checker\"]\na9:[\"prompt\",\"image_url\"]\n93:{\"title\":\"FramePackRequest\",\"type\":\"object\",\"properties\":\"$94\",\"x-fal-order-properties\":\"$a8\",\"required\":\"$a9\"}\nad:[\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\"]\nac:{\"examples\":\"$ad\",\"title\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\"}\naf:[\"16:9\",\"9:16\"]\nae:{\"enum\":\"$af\",\"title\":\"Aspect Ratio (W:H)\",\"type\":\"string\",\"description\":\"The aspect ratio of the video to generate.\",\"default\":\"16:9\"}\nb1:[\"720p\",\"480p\"]\nb0:{\"enum\":\"$b1\",\"title\":\"Resolution\",\"type\":\"string\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.\",\"default\":\"480p\"}\nb2:{\"minimum\":30,\"title\":\"Number of Frames\",\"type\":\"integer\",\"maximum\":1800,\"description\":\"The number of frames to generate.\",\"default\":240}\nb4:[true]\nb3:{\"examples\":\"$b4\",\"title\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"default\":false}\nb"])</script><script>self.__next_f.push([1,"6:[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\"]\nb5:{\"examples\":\"$b6\",\"title\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\"}\nb7:{\"minimum\":0,\"title\":\"Strength of last frame\",\"type\":\"number\",\"maximum\":1,\"description\":\"Determines the influence of the final frame on the generated video. Higher values result in the output being more heavily influenced by the last frame.\",\"default\":0.8}\nb8:{\"minimum\":0,\"title\":\"Guidance Scale\",\"type\":\"number\",\"maximum\":32,\"description\":\"Guidance scale for the generation.\",\"default\":10}\nbb:{\"type\":\"integer\"}\nbc:{\"type\":\"null\"}\nba:[\"$bb\",\"$bc\"]\nb9:{\"anyOf\":\"$ba\",\"title\":\"Seed\",\"description\":\"The seed to use for generating the video.\"}\nbe:[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\"]\nbd:{\"examples\":\"$be\",\"title\":\"End Image Url\",\"type\":\"string\",\"description\":\"URL of the end image input.\"}\nc0:[\"Ugly, blurry distorted, bad quality\"]\nbf:{\"examples\":\"$c0\",\"title\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation.\",\"default\":\"\"}\nc1:{\"minimum\":0,\"title\":\"CFG Scale\",\"type\":\"number\",\"maximum\":7,\"description\":\"Classifier-Free Guidance scale for the generation.\",\"default\":1}\nab:{\"prompt\":\"$ac\",\"aspect_ratio\":\"$ae\",\"resolution\":\"$b0\",\"num_frames\":\"$b2\",\"enable_safety_checker\":\"$b3\",\"image_url\":\"$b5\",\"strength\":\"$b7\",\"guidance_scale\":\"$b8\",\"seed\":\"$b9\",\"end_image_url\":\"$bd\",\"negative_prompt\":\"$bf\",\"cfg_scale\":\"$c1\"}\nc2:[\"prompt\",\"negative_prompt\",\"image_url\",\"seed\",\"aspect_ratio\",\"resolution\",\"cfg_scale\",\"guidance_scale\",\"num_frames\",\"enable_safety_checker\",\"end_image_url\",\"strength\"]\nc3:[\"prompt\",\"image_url\",\"end_image_url\"]\naa:{\"title\":\"FramePackF2LFRequest\",\"type\":\"object\",\"properties\":\"$ab\",\"x-fal-order-properties\":\"$c2\",\"required\":\"$c3\"}\nc8:{\"type\":\"integer\"}\nc9:{\"type\":\"null\"}\nc7:[\"$c8\",\"$c9\"]\nca:[4404019]\nc6:{\"anyOf\":\"$c7\",\"title\":\"File Size\",\"examples\":\"$ca\",\"description\":\"The size of the file in bytes.\"}\ncd:{\"type\":\"string\"}\nce:{\"type\":\"null\"}\ncc:[\"$cd\",\"$ce"])</script><script>self.__next_f.push([1,"\"]\ncf:[\"z9RV14K95DvU.png\"]\ncb:{\"anyOf\":\"$cc\",\"title\":\"File Name\",\"examples\":\"$cf\",\"description\":\"The name of the file. It will be auto-generated if not provided.\"}\nd2:{\"type\":\"string\"}\nd3:{\"type\":\"null\"}\nd1:[\"$d2\",\"$d3\"]\nd4:[\"image/png\"]\nd0:{\"anyOf\":\"$d1\",\"title\":\"Content Type\",\"examples\":\"$d4\",\"description\":\"The mime type of the file.\"}\nd5:{\"title\":\"Url\",\"type\":\"string\",\"description\":\"The URL where the file can be downloaded from.\"}\nc5:{\"file_size\":\"$c6\",\"file_name\":\"$cb\",\"content_type\":\"$d0\",\"url\":\"$d5\"}\nd6:[\"url\",\"content_type\",\"file_name\",\"file_size\"]\nd7:[\"url\"]\nc4:{\"title\":\"File\",\"type\":\"object\",\"properties\":\"$c5\",\"x-fal-order-properties\":\"$d6\",\"required\":\"$d7\"}\nda:{\"title\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\"}\ndd:{\"url\":\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\"}\ndc:[\"$dd\"]\ndb:{\"examples\":\"$dc\",\"$ref\":\"#/components/schemas/File\"}\nd9:{\"seed\":\"$da\",\"video\":\"$db\"}\nde:[\"video\",\"seed\"]\ndf:[\"video\",\"seed\"]\nd8:{\"title\":\"FramePackFLF2VResponse\",\"type\":\"object\",\"properties\":\"$d9\",\"x-fal-order-properties\":\"$de\",\"required\":\"$df\"}\ne3:{\"$ref\":\"#/components/schemas/ValidationError\"}\ne2:{\"title\":\"Detail\",\"type\":\"array\",\"items\":\"$e3\"}\ne1:{\"detail\":\"$e2\"}\ne4:[\"detail\"]\ne0:{\"title\":\"HTTPValidationError\",\"type\":\"object\",\"properties\":\"$e1\",\"x-fal-order-properties\":\"$e4\"}\n60:{\"FramePackResponse\":\"$61\",\"FramePackF1Response\":\"$69\",\"FramePackF1Request\":\"$71\",\"ValidationError\":\"$88\",\"FramePackRequest\":\"$93\",\"FramePackF2LFRequest\":\"$aa\",\"File\":\"$c4\",\"FramePackFLF2VResponse\":\"$d8\",\"HTTPValidationError\":\"$e0\"}\n5f:{\"schemas\":\"$60\"}\ne5:[\"/f1\",\"/flf2v\",\"/\",\"/health\"]\n28:{\"paths\":\"$29\",\"info\":\"$5e\",\"components\":\"$5f\",\"openapi\":\"3.1.0\",\"x-fal-order-paths\":\"$e5\"}\n27:{\"openapi\":\"$28\"}\n25:{\"app_name\":\"framepack\",\"auth_mode\":\"shared\",\"machine_type\":\"GPU-H100\",\"application_id\":\"c17ed161-e42b-4e07-bbbf-99298fdb0c45\",\"keep_alive\":1200,\"min_concurrency\":1,\"max_concurrency\":25,\"concurrency_buffer\":0,\"max_multiplexing\":1,\"request_timeout\":14400,\"startup_timeout\":600,\"valid_regi"])</script><script>self.__next_f.push([1,"ons\":\"$26\",\"updated_at\":\"2025-05-14T16:21:55.575197+00:00\",\"metadata\":\"$27\"}\ne8:[\"image to video\",\"motion\"]\ne9:{\"key\":\"framepack\",\"label\":\"Image-to-Video\"}\nea:[]\neb:[]\ne7:{\"id\":\"fal-ai/framepack\",\"modelId\":\"d10ea3nqn7dbfsbcfb3g\",\"isFavorited\":false,\"title\":\"Framepack\",\"category\":\"image-to-video\",\"tags\":\"$e8\",\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://v3.fal.media/files/koala/dUfFd9Z7aSX06gL2_qXn0_image.webp\",\"modelUrl\":\"https://fal.run/fal-ai/framepack\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-04-17T17:10:10.829Z\",\"creditsRequired\":2,\"group\":\"$e9\",\"machineType\":null,\"examples\":\"$ea\",\"durationEstimate\":3,\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":\"$eb\"}\ned:[\"image to video\",\"motion\"]\nee:{\"key\":\"framepack\",\"label\":\"F1\"}\nef:[]\nf0:[]\nec:{\"id\":\"fal-ai/framepack/f1\",\"modelId\":\"d10ea1fqn7dbfsbcf9ug\",\"isFavorited\":false,\"title\":\"Framepack F1\",\"category\":\"image-to-video\",\"tags\":\"$ed\",\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://v3.fal.media/files/koala/dUfFd9Z7aSX06gL2_qXn0_image.webp\",\"modelUrl\":\"https://fal.run/fal-ai/framepack/f1\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-05-13T22:19:18.611Z\",\"creditsRequired\":2,\"group\":\"$ee\",\"machineType\":null,\"examples\":\"$ef\",\"durationEstimate\":3,\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":\"$f0\"}\nf2:[\"image to video\",\"motion\"]\nf3:{\"key\":\"framepack\",\"label\":\"First-to-Last-Frame\"}\nf4:[]\nf5:[]\nf1:{\"id\":\"fal-ai/framepack/flf2v\",\"modelId\":\"d10ea3fqn7dbfsbcfatg\",\"isFavorited\":false,\"title\":\"Framepack\",\"category\":\"image-to-video\",\"tags\":\"$f2\",\"shortDescription\""])</script><script>self.__next_f.push([1,":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://storage.googleapis.com/fal_cdn/fal/Sound-5.jpg\",\"modelUrl\":\"https://fal.run/fal-ai/framepack/flf2v\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-04-22T20:06:23.895Z\",\"group\":\"$f3\",\"machineType\":null,\"examples\":\"$f4\",\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":\"$f5\"}\ne6:[\"$e7\",\"$ec\",\"$f1\"]\nfb:{\"kind\":\"type\",\"name\":\"File\"}\nfd:{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}\nfc:[\"$fd\"]\nfe:{}\nfa:{\"name\":\"video\",\"label\":\"Video\",\"type\":\"$fb\",\"description\":\"\",\"required\":true,\"examples\":\"$fc\",\"ui\":\"$fe\"}\n100:{}\nff:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":\"$100\"}\nf9:{\"video\":\"$fa\",\"seed\":\"$ff\"}\n101:{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\"}\nf8:{\"name\":\"FramePackResponse\",\"description\":\"\",\"properties\":\"$f9\",\"examples\":\"$101\"}\n105:{\"kind\":\"type\",\"name\":\"File\"}\n107:{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}\n106:[\"$107\"]\n108:{}\n104:{\"name\":\"video\",\"label\":\"Video\",\"type\":\"$105\",\"description\":\"\",\"required\":true,\"examples\":\"$106\",\"ui\":\"$108\"}\n10a:{}\n109:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":\"$10a\"}\n103:{\"video\":\"$104\",\"seed\":\"$109\"}\n10b:{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n"])</script><script>self.__next_f.push([1,"    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\"}\n102:{\"name\":\"FramePackF1Response\",\"description\":\"\",\"properties\":\"$103\",\"examples\":\"$10b\"}\n10f:[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"]\n110:{}\n10e:{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":\"$10f\",\"ui\":\"$110\"}\n112:[\"Ugly, blurry distorted, bad quality\"]\n113:{}\n111:{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":\"$112\",\"defaultValue\":\"\",\"ui\":\"$113\"}\n115:[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"]\n116:{}\n114:{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":\"$115\",\"ui\":\"$116\"}\n118:{}\n117:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":\"$118\"}\n11b:[\"16:9\",\"9:16\"]\n11a:{\"kind\":\"enum\",\"values\":\"$11b\",\"name\":\"AspectRatio(W:H)Enum\"}\n11c:[\"16:9\",\"9:16\"]\n11d:{}\n119:{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":\"$11a\",\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":\"$11c\",\"defaultValue\":\"16:9\",\"ui\":\"$11d\"}\n120:[\"720p\",\"480p\"]\n11f:{\"kind\":\"enum\",\"values\":\"$120\",\"name\":\"ResolutionEnum\"}\n121:[\"720p\",\"480p\"]\n122:{}\n11e:{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":\"$11f\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":\"$121\",\"defaultValue\":\"480p\",\"ui\":\"$122\"}\n124:{}\n123:{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the ge"])</script><script>self.__next_f.push([1,"neration. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":\"$124\"}\n126:{}\n125:{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":\"$126\"}\n128:{}\n127:{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `180`\",\"required\":false,\"defaultValue\":180,\"min\":30,\"max\":900,\"ui\":\"$128\"}\n12a:[true]\n12b:{}\n129:{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":\"$12a\",\"defaultValue\":false,\"ui\":\"$12b\"}\n10d:{\"prompt\":\"$10e\",\"negative_prompt\":\"$111\",\"image_url\":\"$114\",\"seed\":\"$117\",\"aspect_ratio\":\"$119\",\"resolution\":\"$11e\",\"cfg_scale\":\"$123\",\"guidance_scale\":\"$125\",\"num_frames\":\"$127\",\"enable_safety_checker\":\"$129\"}\n12c:{\"all\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 180,\\n  \\\"enable_safety_checker\\\": true\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\"\\n}\"}\n10c:{\"name\":\"FramePackF1Request\",\"description\":\"\",\"properties\":\"$10d\",\"examples\":\"$12c\"}\n130:[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overa"])</script><script>self.__next_f.push([1,"ll scene is lifelike.\"]\n131:{}\n12f:{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":\"$130\",\"ui\":\"$131\"}\n133:[\"Ugly, blurry distorted, bad quality\"]\n134:{}\n132:{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":\"$133\",\"defaultValue\":\"\",\"ui\":\"$134\"}\n136:[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"]\n137:{}\n135:{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":\"$136\",\"ui\":\"$137\"}\n139:{}\n138:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":\"$139\"}\n13c:[\"16:9\",\"9:16\"]\n13b:{\"kind\":\"enum\",\"values\":\"$13c\",\"name\":\"AspectRatio(W:H)Enum\"}\n13d:[\"16:9\",\"9:16\"]\n13e:{}\n13a:{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":\"$13b\",\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":\"$13d\",\"defaultValue\":\"16:9\",\"ui\":\"$13e\"}\n141:[\"720p\",\"480p\"]\n140:{\"kind\":\"enum\",\"values\":\"$141\",\"name\":\"ResolutionEnum\"}\n142:[\"720p\",\"480p\"]\n143:{}\n13f:{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":\"$140\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":\"$142\",\"defaultValue\":\"480p\",\"ui\":\"$143\"}\n145:{}\n144:{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":\"$145\"}\n147:{}\n146:{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":\"$147\"}\n149:{}\n148:{\"name\":\"num_frames\",\"label\":\"Number of Frames\","])</script><script>self.__next_f.push([1,"\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `180`\",\"required\":false,\"defaultValue\":180,\"min\":30,\"max\":900,\"ui\":\"$149\"}\n14b:[true]\n14c:{}\n14a:{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":\"$14b\",\"defaultValue\":false,\"ui\":\"$14c\"}\n12e:{\"prompt\":\"$12f\",\"negative_prompt\":\"$132\",\"image_url\":\"$135\",\"seed\":\"$138\",\"aspect_ratio\":\"$13a\",\"resolution\":\"$13f\",\"cfg_scale\":\"$144\",\"guidance_scale\":\"$146\",\"num_frames\":\"$148\",\"enable_safety_checker\":\"$14a\"}\n14d:{\"all\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 180,\\n  \\\"enable_safety_checker\\\": true\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\"\\n}\"}\n12d:{\"name\":\"FramePackRequest\",\"description\":\"\",\"properties\":\"$12e\",\"examples\":\"$14d\"}\n151:[\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\"]\n152:{}\n150:{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":\"$151\",\"ui\":\"$152\"}\n154:[\"Ugly, blurry distorted, bad quality\"]\n155:{}\n153:{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video gen"])</script><script>self.__next_f.push([1,"eration. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":\"$154\",\"defaultValue\":\"\",\"ui\":\"$155\"}\n157:[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\"]\n158:{}\n156:{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":\"$157\",\"ui\":\"$158\"}\n15a:{}\n159:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":\"$15a\"}\n15d:[\"16:9\",\"9:16\"]\n15c:{\"kind\":\"enum\",\"values\":\"$15d\",\"name\":\"AspectRatio(W:H)Enum\"}\n15e:[\"16:9\",\"9:16\"]\n15f:{}\n15b:{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":\"$15c\",\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":\"$15e\",\"defaultValue\":\"16:9\",\"ui\":\"$15f\"}\n162:[\"720p\",\"480p\"]\n161:{\"kind\":\"enum\",\"values\":\"$162\",\"name\":\"ResolutionEnum\"}\n163:[\"720p\",\"480p\"]\n164:{}\n160:{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":\"$161\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":\"$163\",\"defaultValue\":\"480p\",\"ui\":\"$164\"}\n166:{}\n165:{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":\"$166\"}\n168:{}\n167:{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":\"$168\"}\n16a:{}\n169:{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `240`\",\"required\":false,\"defaultValue\":240,\"min\":30,\"max\":1800,\"ui\":\"$16a\"}\n16c:[true]\n16d:{}\n16b:{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":\""])</script><script>self.__next_f.push([1,"$16c\",\"defaultValue\":false,\"ui\":\"$16d\"}\n16f:[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\"]\n170:{}\n16e:{\"name\":\"end_image_url\",\"label\":\"End Image Url\",\"type\":\"string\",\"description\":\"URL of the end image input.\",\"required\":true,\"examples\":\"$16f\",\"ui\":\"$170\"}\n172:{}\n171:{\"name\":\"strength\",\"label\":\"Strength of last frame\",\"type\":\"float\",\"description\":\"Determines the influence of the final frame on the generated video. Higher values result in the output being more heavily influenced by the last frame. Default value: `0.8`\",\"required\":false,\"defaultValue\":0.8,\"min\":0,\"max\":1,\"ui\":\"$172\"}\n14f:{\"prompt\":\"$150\",\"negative_prompt\":\"$153\",\"image_url\":\"$156\",\"seed\":\"$159\",\"aspect_ratio\":\"$15b\",\"resolution\":\"$160\",\"cfg_scale\":\"$165\",\"guidance_scale\":\"$167\",\"num_frames\":\"$169\",\"enable_safety_checker\":\"$16b\",\"end_image_url\":\"$16e\",\"strength\":\"$171\"}\n173:{\"all\":\"{\\n  \\\"prompt\\\": \\\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 240,\\n  \\\"enable_safety_checker\\\": true,\\n  \\\"end_image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\\\",\\n  \\\"strength\\\": 0.8\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\\\",\\n  \\\"end_image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\\\"\\n}\"}\n14e:{\"name\":\"FramePackF2LFRequest\",\"description\":\"\",\"properties\":\"$14f\",\"examp"])</script><script>self.__next_f.push([1,"les\":\"$173\"}\n177:{}\n176:{\"name\":\"url\",\"label\":\"Url\",\"type\":\"string\",\"description\":\"The URL where the file can be downloaded from.\",\"required\":true,\"ui\":\"$177\"}\n179:[\"image/png\"]\n17a:{}\n178:{\"name\":\"content_type\",\"label\":\"Content Type\",\"type\":\"string\",\"description\":\"The mime type of the file.\",\"required\":false,\"examples\":\"$179\",\"ui\":\"$17a\"}\n17c:[\"z9RV14K95DvU.png\"]\n17d:{}\n17b:{\"name\":\"file_name\",\"label\":\"File Name\",\"type\":\"string\",\"description\":\"The name of the file. It will be auto-generated if not provided.\",\"required\":false,\"examples\":\"$17c\",\"ui\":\"$17d\"}\n17f:[4404019]\n180:{}\n17e:{\"name\":\"file_size\",\"label\":\"File Size\",\"type\":\"integer\",\"description\":\"The size of the file in bytes.\",\"required\":false,\"examples\":\"$17f\",\"ui\":\"$180\"}\n175:{\"url\":\"$176\",\"content_type\":\"$178\",\"file_name\":\"$17b\",\"file_size\":\"$17e\"}\n181:{\"all\":\"{\\n  \\\"url\\\": \\\"\\\",\\n  \\\"content_type\\\": \\\"image/png\\\",\\n  \\\"file_name\\\": \\\"z9RV14K95DvU.png\\\",\\n  \\\"file_size\\\": 4404019\\n}\",\"required\":\"{\\n  \\\"url\\\": \\\"\\\"\\n}\"}\n174:{\"name\":\"File\",\"description\":\"\",\"properties\":\"$175\",\"examples\":\"$181\"}\n185:{\"kind\":\"type\",\"name\":\"File\"}\n187:{\"url\":\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\"}\n186:[\"$187\"]\n188:{}\n184:{\"name\":\"video\",\"label\":\"Video\",\"type\":\"$185\",\"description\":\"\",\"required\":true,\"examples\":\"$186\",\"ui\":\"$188\"}\n18a:{}\n189:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":\"$18a\"}\n183:{\"video\":\"$184\",\"seed\":\"$189\"}\n18b:{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\\\"\\n  }\\n}\"}\n182:{\"name\":\"FramePackFLF2VResponse\",\"description\":\"\",\"properties\":\"$183\",\"examples\":\"$18b\"}\nf7:{\"FramePackResponse\":\"$f8\",\"FramePackF1Response\":\"$102\",\"FramePackF1Request\":\"$10c\",\"FramePackRequest\":\"$12d\",\"FramePackF2LFRequest\":\"$14e\",\"File\":\"$174\",\"FramePackFLF2VResponse\":\"$182\"}\n18e:[\"Fra"])</script><script>self.__next_f.push([1,"mePackF1Request\"]\n18f:{\"url\":\"https://fal.run/fal-ai/framepack/f1\",\"method\":\"post\"}\n18d:{\"name\":\"generate_video_f1_endpoint_f1_post\",\"description\":\"Generate Video F1 Endpoint\",\"path\":\"/f1\",\"inputs\":\"$18e\",\"output\":\"FramePackF1Response\",\"endpoint\":\"$18f\"}\n191:[\"FramePackRequest\"]\n192:{\"url\":\"https://fal.run/fal-ai/framepack\",\"method\":\"post\"}\n190:{\"name\":\"generate_video_regular_endpoint\",\"description\":\"Generate Video Regular Endpoint\",\"path\":\"\",\"inputs\":\"$191\",\"output\":\"FramePackResponse\",\"endpoint\":\"$192\"}\n194:[\"FramePackF2LFRequest\"]\n195:{\"url\":\"https://fal.run/fal-ai/framepack/flf2v\",\"method\":\"post\"}\n193:{\"name\":\"generate_video_flf2v_endpoint_flf2v_post\",\"description\":\"Generate Video Flf2V Endpoint\",\"path\":\"/flf2v\",\"inputs\":\"$194\",\"output\":\"FramePackFLF2VResponse\",\"endpoint\":\"$195\"}\n197:[]\n198:{\"url\":\"https://fal.run/fal-ai/framepack/health\",\"method\":\"get\"}\n196:{\"name\":\"health_health_get\",\"description\":\"Health\",\"path\":\"/health\",\"inputs\":\"$197\",\"output\":\"void\",\"endpoint\":\"$198\"}\n18c:[\"$18d\",\"$190\",\"$193\",\"$196\"]\n19f:[\"string\",\"integer\"]\n19e:{\"kind\":\"union\",\"types\":\"$19f\"}\n19d:{\"kind\":\"list\",\"elementType\":\"$19e\"}\n1a0:{}\n19c:{\"name\":\"loc\",\"label\":\"Location\",\"type\":\"$19d\",\"description\":\"\",\"required\":true,\"ui\":\"$1a0\"}\n1a2:{}\n1a1:{\"name\":\"msg\",\"label\":\"Message\",\"type\":\"string\",\"description\":\"\",\"required\":true,\"ui\":\"$1a2\"}\n1a4:{}\n1a3:{\"name\":\"type\",\"label\":\"Error Type\",\"type\":\"string\",\"description\":\"\",\"required\":true,\"ui\":\"$1a4\"}\n19b:{\"loc\":\"$19c\",\"msg\":\"$1a1\",\"type\":\"$1a3\"}\n1a5:{\"all\":\"{\\n  \\\"msg\\\": \\\"\\\",\\n  \\\"type\\\": \\\"\\\"\\n}\",\"required\":\"{\\n  \\\"msg\\\": \\\"\\\",\\n  \\\"type\\\": \\\"\\\"\\n}\"}\n19a:{\"name\":\"ValidationError\",\"description\":\"When input validation fails, the API will response with a `422` status and an array\\nof `ValidationError` object in the response body. The `ValidationError` object\\nwill contain a list of errors, each with a `loc` (location) attribute that indicates\\nthe path to the invalid input, and a `msg` (message) attribute that describes the error.\",\"properties\":\"$19b\",\"examples\":\"$1a5\"}\n1aa:{\"kind\":\"type\",\""])</script><script>self.__next_f.push([1,"name\":\"ValidationError\"}\n1a9:{\"kind\":\"list\",\"elementType\":\"$1aa\"}\n1ab:{}\n1a8:{\"name\":\"detail\",\"label\":\"Detail\",\"type\":\"$1a9\",\"description\":\"\",\"required\":false,\"ui\":\"$1ab\"}\n1a7:{\"detail\":\"$1a8\"}\n1ac:{\"all\":\"{\\n  \\\"detail\\\": [\\n    {\\n      \\\"msg\\\": \\\"\\\",\\n      \\\"type\\\": \\\"\\\"\\n    }\\n  ]\\n}\",\"required\":\"{}\"}\n1a6:{\"name\":\"HTTPValidationError\",\"description\":\"\",\"properties\":\"$1a7\",\"examples\":\"$1ac\"}\n199:{\"ValidationError\":\"$19a\",\"HTTPValidationError\":\"$1a6\"}\nf6:{\"name\":\"Framepack\",\"baseUrl\":\"https://fal.run/fal-ai/framepack\",\"types\":\"$f7\",\"functions\":\"$18c\",\"errorTypes\":\"$199\"}\n1ae:[\"FramePackRequest\"]\n1af:{\"url\":\"https://fal.run/fal-ai/framepack\",\"method\":\"post\"}\n1ad:{\"name\":\"generate_video_regular_endpoint\",\"description\":\"Generate Video Regular Endpoint\",\"path\":\"\",\"inputs\":\"$1ae\",\"output\":\"FramePackResponse\",\"endpoint\":\"$1af\"}\n1b3:[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"]\n1b4:{}\n1b2:{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":\"$1b3\",\"ui\":\"$1b4\"}\n1b6:[\"Ugly, blurry distorted, bad quality\"]\n1b7:{}\n1b5:{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":\"$1b6\",\"defaultValue\":\"\",\"ui\":\"$1b7\"}\n1b9:[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"]\n1ba:{}\n1b8:{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":\"$1b9\",\"ui\":\"$1ba\"}\n1bc:{}\n1bb:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":\"$1bc\"}\n1bf:[\"16:9\",\"9:16\"]\n1be:{\"kind\":\"enum\",\"values\":\"$1bf\",\"name\":\"AspectRatio(W:H)Enum\"}\n1c0:[\"16:9\",\"9:16\"]\n1c1:{}\n1bd:{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":\"$1be\",\"description\":\"The aspect rati"])</script><script>self.__next_f.push([1,"o of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":\"$1c0\",\"defaultValue\":\"16:9\",\"ui\":\"$1c1\"}\n1c4:[\"720p\",\"480p\"]\n1c3:{\"kind\":\"enum\",\"values\":\"$1c4\",\"name\":\"ResolutionEnum\"}\n1c5:[\"720p\",\"480p\"]\n1c6:{}\n1c2:{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":\"$1c3\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":\"$1c5\",\"defaultValue\":\"480p\",\"ui\":\"$1c6\"}\n1c8:{}\n1c7:{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":\"$1c8\"}\n1ca:{}\n1c9:{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":\"$1ca\"}\n1cc:{}\n1cb:{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `180`\",\"required\":false,\"defaultValue\":180,\"min\":30,\"max\":900,\"ui\":\"$1cc\"}\n1ce:[true]\n1cf:{}\n1cd:{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":\"$1ce\",\"defaultValue\":false,\"ui\":\"$1cf\"}\n1b1:{\"prompt\":\"$1b2\",\"negative_prompt\":\"$1b5\",\"image_url\":\"$1b8\",\"seed\":\"$1bb\",\"aspect_ratio\":\"$1bd\",\"resolution\":\"$1c2\",\"cfg_scale\":\"$1c7\",\"guidance_scale\":\"$1c9\",\"num_frames\":\"$1cb\",\"enable_safety_checker\":\"$1cd\"}\n1d0:{\"all\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\""])</script><script>self.__next_f.push([1,"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 180,\\n  \\\"enable_safety_checker\\\": true\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\"\\n}\"}\n1b0:{\"name\":\"FramePackRequest\",\"description\":\"\",\"properties\":\"$1b1\",\"examples\":\"$1d0\"}\n1d4:{\"kind\":\"type\",\"name\":\"File\"}\n1d6:{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}\n1d5:[\"$1d6\"]\n1d7:{}\n1d3:{\"name\":\"video\",\"label\":\"Video\",\"type\":\"$1d4\",\"description\":\"\",\"required\":true,\"examples\":\"$1d5\",\"ui\":\"$1d7\"}\n1d9:{}\n1d8:{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":\"$1d9\"}\n1d2:{\"video\":\"$1d3\",\"seed\":\"$1d8\"}\n1da:{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\"}\n1d1:{\"name\":\"FramePackResponse\",\"description\":\"\",\"properties\":\"$1d2\",\"examples\":\"$1da\"}\n1db:[\"endpointId\",\"fal-ai%2Fframepack\",\"d\"]\n"])</script><script>self.__next_f.push([1,"f:[\"$\",\"$L1c\",null,{\"endpointId\":\"fal-ai/framepack\",\"model\":{\"id\":\"fal-ai/framepack\",\"modelId\":\"d10ea3nqn7dbfsbcfb3g\",\"isFavorited\":false,\"title\":\"Framepack\",\"category\":\"image-to-video\",\"tags\":[\"image to video\",\"motion\"],\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://v3.fal.media/files/koala/dUfFd9Z7aSX06gL2_qXn0_image.webp\",\"modelUrl\":\"https://fal.run/fal-ai/framepack\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-04-17T17:10:10.829Z\",\"creditsRequired\":2,\"group\":{\"key\":\"framepack\",\"label\":\"Image-to-Video\"},\"machineType\":null,\"examples\":[],\"durationEstimate\":3,\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":[]},\"modelData\":{\"endpointId\":\"fal-ai/framepack\",\"model\":\"$1d\",\"appData\":{\"app_name\":\"framepack\",\"auth_mode\":\"shared\",\"machine_type\":\"GPU-H100\",\"application_id\":\"c17ed161-e42b-4e07-bbbf-99298fdb0c45\",\"keep_alive\":1200,\"min_concurrency\":1,\"max_concurrency\":25,\"concurrency_buffer\":0,\"max_multiplexing\":1,\"request_timeout\":14400,\"startup_timeout\":600,\"valid_regions\":[\"us-central\",\"us-west\",\"us-east\",\"eu-north\"],\"updated_at\":\"2025-05-14T16:21:55.575197+00:00\",\"metadata\":{\"openapi\":{\"paths\":{\"/f1\":{\"post\":{\"responses\":{\"200\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/FramePackF1Response\"}}},\"description\":\"Successful Response\"},\"422\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/HTTPValidationError\"}}},\"description\":\"Validation Error\"}},\"requestBody\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/FramePackF1Request\"}}},\"required\":true},\"summary\":\"Generate Video F1 Endpoint\",\"operationId\":\"generate_video_f1_endpoint_f1_post\"}},\"/\":{\"post\":{\"responses\":{\"200\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/FramePackResponse\"}}},\"description\":\"Successful Response\"},\"422\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/HTTPValidationError\"}}},\"description\":\"Validation Error\"}},\"requestBody\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/FramePackRequest\"}}},\"required\":true},\"summary\":\"Generate Video Regular Endpoint\",\"operationId\":\"generate_video_regular_endpoint__post\"}},\"/flf2v\":{\"post\":{\"responses\":{\"200\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/FramePackFLF2VResponse\"}}},\"description\":\"Successful Response\"},\"422\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/HTTPValidationError\"}}},\"description\":\"Validation Error\"}},\"requestBody\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/FramePackF2LFRequest\"}}},\"required\":true},\"summary\":\"Generate Video Flf2V Endpoint\",\"operationId\":\"generate_video_flf2v_endpoint_flf2v_post\"}},\"/health\":{\"get\":{\"responses\":{\"200\":{\"content\":{\"application/json\":{\"schema\":{}}},\"description\":\"Successful Response\"}},\"summary\":\"Health\",\"operationId\":\"health_health_get\"}}},\"info\":{\"version\":\"0.1.0\",\"title\":\"FastAPI\"},\"components\":{\"schemas\":{\"FramePackResponse\":{\"title\":\"FramePackResponse\",\"type\":\"object\",\"properties\":{\"seed\":{\"title\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\"},\"video\":{\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}],\"$ref\":\"#/components/schemas/File\"}},\"x-fal-order-properties\":[\"video\",\"seed\"],\"required\":[\"video\",\"seed\"]},\"FramePackF1Response\":{\"title\":\"FramePackF1Response\",\"type\":\"object\",\"properties\":{\"seed\":{\"title\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\"},\"video\":{\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}],\"$ref\":\"#/components/schemas/File\"}},\"x-fal-order-properties\":[\"video\",\"seed\"],\"required\":[\"video\",\"seed\"]},\"FramePackF1Request\":{\"title\":\"FramePackF1Request\",\"type\":\"object\",\"properties\":{\"prompt\":{\"examples\":[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"],\"title\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\"},\"aspect_ratio\":{\"enum\":[\"16:9\",\"9:16\"],\"title\":\"Aspect Ratio (W:H)\",\"type\":\"string\",\"description\":\"The aspect ratio of the video to generate.\",\"default\":\"16:9\"},\"resolution\":{\"enum\":[\"720p\",\"480p\"],\"title\":\"Resolution\",\"type\":\"string\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.\",\"default\":\"480p\"},\"num_frames\":{\"minimum\":30,\"title\":\"Number of Frames\",\"type\":\"integer\",\"maximum\":900,\"description\":\"The number of frames to generate.\",\"default\":180},\"image_url\":{\"examples\":[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"],\"title\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\"},\"guidance_scale\":{\"minimum\":0,\"title\":\"Guidance Scale\",\"type\":\"number\",\"maximum\":32,\"description\":\"Guidance scale for the generation.\",\"default\":10},\"seed\":{\"anyOf\":[{\"type\":\"integer\"},{\"type\":\"null\"}],\"title\":\"Seed\",\"description\":\"The seed to use for generating the video.\"},\"enable_safety_checker\":{\"examples\":[true],\"title\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"default\":false},\"negative_prompt\":{\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"title\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation.\",\"default\":\"\"},\"cfg_scale\":{\"minimum\":0,\"title\":\"CFG Scale\",\"type\":\"number\",\"maximum\":7,\"description\":\"Classifier-Free Guidance scale for the generation.\",\"default\":1}},\"x-fal-order-properties\":[\"prompt\",\"negative_prompt\",\"image_url\",\"seed\",\"aspect_ratio\",\"resolution\",\"cfg_scale\",\"guidance_scale\",\"num_frames\",\"enable_safety_checker\"],\"required\":[\"prompt\",\"image_url\"]},\"ValidationError\":{\"title\":\"ValidationError\",\"type\":\"object\",\"properties\":{\"type\":{\"title\":\"Error Type\",\"type\":\"string\"},\"msg\":{\"title\":\"Message\",\"type\":\"string\"},\"loc\":{\"title\":\"Location\",\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"string\"},{\"type\":\"integer\"}]}}},\"x-fal-order-properties\":[\"loc\",\"msg\",\"type\"],\"required\":[\"loc\",\"msg\",\"type\"]},\"FramePackRequest\":{\"title\":\"FramePackRequest\",\"type\":\"object\",\"properties\":{\"prompt\":{\"examples\":[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"],\"title\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\"},\"aspect_ratio\":{\"enum\":[\"16:9\",\"9:16\"],\"title\":\"Aspect Ratio (W:H)\",\"type\":\"string\",\"description\":\"The aspect ratio of the video to generate.\",\"default\":\"16:9\"},\"resolution\":{\"enum\":[\"720p\",\"480p\"],\"title\":\"Resolution\",\"type\":\"string\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.\",\"default\":\"480p\"},\"num_frames\":{\"minimum\":30,\"title\":\"Number of Frames\",\"type\":\"integer\",\"maximum\":900,\"description\":\"The number of frames to generate.\",\"default\":180},\"image_url\":{\"examples\":[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"],\"title\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\"},\"guidance_scale\":{\"minimum\":0,\"title\":\"Guidance Scale\",\"type\":\"number\",\"maximum\":32,\"description\":\"Guidance scale for the generation.\",\"default\":10},\"seed\":{\"anyOf\":[{\"type\":\"integer\"},{\"type\":\"null\"}],\"title\":\"Seed\",\"description\":\"The seed to use for generating the video.\"},\"enable_safety_checker\":{\"examples\":[true],\"title\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"default\":false},\"negative_prompt\":{\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"title\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation.\",\"default\":\"\"},\"cfg_scale\":{\"minimum\":0,\"title\":\"CFG Scale\",\"type\":\"number\",\"maximum\":7,\"description\":\"Classifier-Free Guidance scale for the generation.\",\"default\":1}},\"x-fal-order-properties\":[\"prompt\",\"negative_prompt\",\"image_url\",\"seed\",\"aspect_ratio\",\"resolution\",\"cfg_scale\",\"guidance_scale\",\"num_frames\",\"enable_safety_checker\"],\"required\":[\"prompt\",\"image_url\"]},\"FramePackF2LFRequest\":{\"title\":\"FramePackF2LFRequest\",\"type\":\"object\",\"properties\":{\"prompt\":{\"examples\":[\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\"],\"title\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\"},\"aspect_ratio\":{\"enum\":[\"16:9\",\"9:16\"],\"title\":\"Aspect Ratio (W:H)\",\"type\":\"string\",\"description\":\"The aspect ratio of the video to generate.\",\"default\":\"16:9\"},\"resolution\":{\"enum\":[\"720p\",\"480p\"],\"title\":\"Resolution\",\"type\":\"string\",\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.\",\"default\":\"480p\"},\"num_frames\":{\"minimum\":30,\"title\":\"Number of Frames\",\"type\":\"integer\",\"maximum\":1800,\"description\":\"The number of frames to generate.\",\"default\":240},\"enable_safety_checker\":{\"examples\":[true],\"title\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"default\":false},\"image_url\":{\"examples\":[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\"],\"title\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\"},\"strength\":{\"minimum\":0,\"title\":\"Strength of last frame\",\"type\":\"number\",\"maximum\":1,\"description\":\"Determines the influence of the final frame on the generated video. Higher values result in the output being more heavily influenced by the last frame.\",\"default\":0.8},\"guidance_scale\":{\"minimum\":0,\"title\":\"Guidance Scale\",\"type\":\"number\",\"maximum\":32,\"description\":\"Guidance scale for the generation.\",\"default\":10},\"seed\":{\"anyOf\":[{\"type\":\"integer\"},{\"type\":\"null\"}],\"title\":\"Seed\",\"description\":\"The seed to use for generating the video.\"},\"end_image_url\":{\"examples\":[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\"],\"title\":\"End Image Url\",\"type\":\"string\",\"description\":\"URL of the end image input.\"},\"negative_prompt\":{\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"title\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation.\",\"default\":\"\"},\"cfg_scale\":{\"minimum\":0,\"title\":\"CFG Scale\",\"type\":\"number\",\"maximum\":7,\"description\":\"Classifier-Free Guidance scale for the generation.\",\"default\":1}},\"x-fal-order-properties\":[\"prompt\",\"negative_prompt\",\"image_url\",\"seed\",\"aspect_ratio\",\"resolution\",\"cfg_scale\",\"guidance_scale\",\"num_frames\",\"enable_safety_checker\",\"end_image_url\",\"strength\"],\"required\":[\"prompt\",\"image_url\",\"end_image_url\"]},\"File\":{\"title\":\"File\",\"type\":\"object\",\"properties\":{\"file_size\":{\"anyOf\":[{\"type\":\"integer\"},{\"type\":\"null\"}],\"title\":\"File Size\",\"examples\":[4404019],\"description\":\"The size of the file in bytes.\"},\"file_name\":{\"anyOf\":[{\"type\":\"string\"},{\"type\":\"null\"}],\"title\":\"File Name\",\"examples\":[\"z9RV14K95DvU.png\"],\"description\":\"The name of the file. It will be auto-generated if not provided.\"},\"content_type\":{\"anyOf\":[{\"type\":\"string\"},{\"type\":\"null\"}],\"title\":\"Content Type\",\"examples\":[\"image/png\"],\"description\":\"The mime type of the file.\"},\"url\":{\"title\":\"Url\",\"type\":\"string\",\"description\":\"The URL where the file can be downloaded from.\"}},\"x-fal-order-properties\":[\"url\",\"content_type\",\"file_name\",\"file_size\"],\"required\":[\"url\"]},\"FramePackFLF2VResponse\":{\"title\":\"FramePackFLF2VResponse\",\"type\":\"object\",\"properties\":{\"seed\":{\"title\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\"},\"video\":{\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\"}],\"$ref\":\"#/components/schemas/File\"}},\"x-fal-order-properties\":[\"video\",\"seed\"],\"required\":[\"video\",\"seed\"]},\"HTTPValidationError\":{\"title\":\"HTTPValidationError\",\"type\":\"object\",\"properties\":{\"detail\":{\"title\":\"Detail\",\"type\":\"array\",\"items\":{\"$ref\":\"#/components/schemas/ValidationError\"}}},\"x-fal-order-properties\":[\"detail\"]}}},\"openapi\":\"3.1.0\",\"x-fal-order-paths\":[\"/f1\",\"/flf2v\",\"/\",\"/health\"]}}},\"modes\":[{\"id\":\"fal-ai/framepack\",\"modelId\":\"d10ea3nqn7dbfsbcfb3g\",\"isFavorited\":false,\"title\":\"Framepack\",\"category\":\"image-to-video\",\"tags\":[\"image to video\",\"motion\"],\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://v3.fal.media/files/koala/dUfFd9Z7aSX06gL2_qXn0_image.webp\",\"modelUrl\":\"https://fal.run/fal-ai/framepack\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-04-17T17:10:10.829Z\",\"creditsRequired\":2,\"group\":{\"key\":\"framepack\",\"label\":\"Image-to-Video\"},\"machineType\":null,\"examples\":[],\"durationEstimate\":3,\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":[]},{\"id\":\"fal-ai/framepack/f1\",\"modelId\":\"d10ea1fqn7dbfsbcf9ug\",\"isFavorited\":false,\"title\":\"Framepack F1\",\"category\":\"image-to-video\",\"tags\":[\"image to video\",\"motion\"],\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://v3.fal.media/files/koala/dUfFd9Z7aSX06gL2_qXn0_image.webp\",\"modelUrl\":\"https://fal.run/fal-ai/framepack/f1\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-05-13T22:19:18.611Z\",\"creditsRequired\":2,\"group\":{\"key\":\"framepack\",\"label\":\"F1\"},\"machineType\":null,\"examples\":[],\"durationEstimate\":3,\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":[]},{\"id\":\"fal-ai/framepack/flf2v\",\"modelId\":\"d10ea3fqn7dbfsbcfatg\",\"isFavorited\":false,\"title\":\"Framepack\",\"category\":\"image-to-video\",\"tags\":[\"image to video\",\"motion\"],\"shortDescription\":\"Framepack is an efficient Image-to-video model that autoregressively generates videos.\",\"thumbnailUrl\":\"https://storage.googleapis.com/fal_cdn/fal/Sound-5.jpg\",\"modelUrl\":\"https://fal.run/fal-ai/framepack/flf2v\",\"githubUrl\":\"\",\"licenseType\":\"commercial\",\"date\":\"2025-04-22T20:06:23.895Z\",\"group\":{\"key\":\"framepack\",\"label\":\"First-to-Last-Frame\"},\"machineType\":null,\"examples\":[],\"highlighted\":false,\"authSkippable\":false,\"unlisted\":false,\"deprecated\":false,\"resultComparison\":false,\"hidePricing\":false,\"private\":false,\"removed\":false,\"adminOnly\":false,\"kind\":\"inference\",\"trainingEndpoints\":[]}],\"metadata\":{\"name\":\"Framepack\",\"baseUrl\":\"https://fal.run/fal-ai/framepack\",\"types\":{\"FramePackResponse\":{\"name\":\"FramePackResponse\",\"description\":\"\",\"properties\":{\"video\":{\"name\":\"video\",\"label\":\"Video\",\"type\":{\"kind\":\"type\",\"name\":\"File\"},\"description\":\"\",\"required\":true,\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\"}},\"FramePackF1Response\":{\"name\":\"FramePackF1Response\",\"description\":\"\",\"properties\":{\"video\":{\"name\":\"video\",\"label\":\"Video\",\"type\":{\"kind\":\"type\",\"name\":\"File\"},\"description\":\"\",\"required\":true,\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\"}},\"FramePackF1Request\":{\"name\":\"FramePackF1Request\",\"description\":\"\",\"properties\":{\"prompt\":{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"],\"ui\":{}},\"negative_prompt\":{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"defaultValue\":\"\",\"ui\":{}},\"image_url\":{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":{}},\"aspect_ratio\":{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":{\"kind\":\"enum\",\"values\":[\"16:9\",\"9:16\"],\"name\":\"AspectRatio(W:H)Enum\"},\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":[\"16:9\",\"9:16\"],\"defaultValue\":\"16:9\",\"ui\":{}},\"resolution\":{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":{\"kind\":\"enum\",\"values\":[\"720p\",\"480p\"],\"name\":\"ResolutionEnum\"},\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":[\"720p\",\"480p\"],\"defaultValue\":\"480p\",\"ui\":{}},\"cfg_scale\":{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":{}},\"guidance_scale\":{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":{}},\"num_frames\":{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `180`\",\"required\":false,\"defaultValue\":180,\"min\":30,\"max\":900,\"ui\":{}},\"enable_safety_checker\":{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":[true],\"defaultValue\":false,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 180,\\n  \\\"enable_safety_checker\\\": true\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\"\\n}\"}},\"FramePackRequest\":{\"name\":\"FramePackRequest\",\"description\":\"\",\"properties\":{\"prompt\":{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"],\"ui\":{}},\"negative_prompt\":{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"defaultValue\":\"\",\"ui\":{}},\"image_url\":{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":{}},\"aspect_ratio\":{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":{\"kind\":\"enum\",\"values\":[\"16:9\",\"9:16\"],\"name\":\"AspectRatio(W:H)Enum\"},\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":[\"16:9\",\"9:16\"],\"defaultValue\":\"16:9\",\"ui\":{}},\"resolution\":{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":{\"kind\":\"enum\",\"values\":[\"720p\",\"480p\"],\"name\":\"ResolutionEnum\"},\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":[\"720p\",\"480p\"],\"defaultValue\":\"480p\",\"ui\":{}},\"cfg_scale\":{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":{}},\"guidance_scale\":{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":{}},\"num_frames\":{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `180`\",\"required\":false,\"defaultValue\":180,\"min\":30,\"max\":900,\"ui\":{}},\"enable_safety_checker\":{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":[true],\"defaultValue\":false,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 180,\\n  \\\"enable_safety_checker\\\": true\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\"\\n}\"}},\"FramePackF2LFRequest\":{\"name\":\"FramePackF2LFRequest\",\"description\":\"\",\"properties\":{\"prompt\":{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":[\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\"],\"ui\":{}},\"negative_prompt\":{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"defaultValue\":\"\",\"ui\":{}},\"image_url\":{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\"],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":{}},\"aspect_ratio\":{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":{\"kind\":\"enum\",\"values\":[\"16:9\",\"9:16\"],\"name\":\"AspectRatio(W:H)Enum\"},\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":[\"16:9\",\"9:16\"],\"defaultValue\":\"16:9\",\"ui\":{}},\"resolution\":{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":{\"kind\":\"enum\",\"values\":[\"720p\",\"480p\"],\"name\":\"ResolutionEnum\"},\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":[\"720p\",\"480p\"],\"defaultValue\":\"480p\",\"ui\":{}},\"cfg_scale\":{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":{}},\"guidance_scale\":{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":{}},\"num_frames\":{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `240`\",\"required\":false,\"defaultValue\":240,\"min\":30,\"max\":1800,\"ui\":{}},\"enable_safety_checker\":{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":[true],\"defaultValue\":false,\"ui\":{}},\"end_image_url\":{\"name\":\"end_image_url\",\"label\":\"End Image Url\",\"type\":\"string\",\"description\":\"URL of the end image input.\",\"required\":true,\"examples\":[\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\"],\"ui\":{}},\"strength\":{\"name\":\"strength\",\"label\":\"Strength of last frame\",\"type\":\"float\",\"description\":\"Determines the influence of the final frame on the generated video. Higher values result in the output being more heavily influenced by the last frame. Default value: `0.8`\",\"required\":false,\"defaultValue\":0.8,\"min\":0,\"max\":1,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"prompt\\\": \\\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 240,\\n  \\\"enable_safety_checker\\\": true,\\n  \\\"end_image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\\\",\\n  \\\"strength\\\": 0.8\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png\\\",\\n  \\\"end_image_url\\\": \\\"https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png\\\"\\n}\"}},\"File\":{\"name\":\"File\",\"description\":\"\",\"properties\":{\"url\":{\"name\":\"url\",\"label\":\"Url\",\"type\":\"string\",\"description\":\"The URL where the file can be downloaded from.\",\"required\":true,\"ui\":{}},\"content_type\":{\"name\":\"content_type\",\"label\":\"Content Type\",\"type\":\"string\",\"description\":\"The mime type of the file.\",\"required\":false,\"examples\":[\"image/png\"],\"ui\":{}},\"file_name\":{\"name\":\"file_name\",\"label\":\"File Name\",\"type\":\"string\",\"description\":\"The name of the file. It will be auto-generated if not provided.\",\"required\":false,\"examples\":[\"z9RV14K95DvU.png\"],\"ui\":{}},\"file_size\":{\"name\":\"file_size\",\"label\":\"File Size\",\"type\":\"integer\",\"description\":\"The size of the file in bytes.\",\"required\":false,\"examples\":[4404019],\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"url\\\": \\\"\\\",\\n  \\\"content_type\\\": \\\"image/png\\\",\\n  \\\"file_name\\\": \\\"z9RV14K95DvU.png\\\",\\n  \\\"file_size\\\": 4404019\\n}\",\"required\":\"{\\n  \\\"url\\\": \\\"\\\"\\n}\"}},\"FramePackFLF2VResponse\":{\"name\":\"FramePackFLF2VResponse\",\"description\":\"\",\"properties\":{\"video\":{\"name\":\"video\",\"label\":\"Video\",\"type\":{\"kind\":\"type\",\"name\":\"File\"},\"description\":\"\",\"required\":true,\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\"}],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/example_outputs/flf2v.mp4\\\"\\n  }\\n}\"}}},\"functions\":[{\"name\":\"generate_video_f1_endpoint_f1_post\",\"description\":\"Generate Video F1 Endpoint\",\"path\":\"/f1\",\"inputs\":[\"FramePackF1Request\"],\"output\":\"FramePackF1Response\",\"endpoint\":{\"url\":\"https://fal.run/fal-ai/framepack/f1\",\"method\":\"post\"}},{\"name\":\"generate_video_regular_endpoint\",\"description\":\"Generate Video Regular Endpoint\",\"path\":\"\",\"inputs\":[\"FramePackRequest\"],\"output\":\"FramePackResponse\",\"endpoint\":{\"url\":\"https://fal.run/fal-ai/framepack\",\"method\":\"post\"}},{\"name\":\"generate_video_flf2v_endpoint_flf2v_post\",\"description\":\"Generate Video Flf2V Endpoint\",\"path\":\"/flf2v\",\"inputs\":[\"FramePackF2LFRequest\"],\"output\":\"FramePackFLF2VResponse\",\"endpoint\":{\"url\":\"https://fal.run/fal-ai/framepack/flf2v\",\"method\":\"post\"}},{\"name\":\"health_health_get\",\"description\":\"Health\",\"path\":\"/health\",\"inputs\":[],\"output\":\"void\",\"endpoint\":{\"url\":\"https://fal.run/fal-ai/framepack/health\",\"method\":\"get\"}}],\"errorTypes\":{\"ValidationError\":{\"name\":\"ValidationError\",\"description\":\"When input validation fails, the API will response with a `422` status and an array\\nof `ValidationError` object in the response body. The `ValidationError` object\\nwill contain a list of errors, each with a `loc` (location) attribute that indicates\\nthe path to the invalid input, and a `msg` (message) attribute that describes the error.\",\"properties\":{\"loc\":{\"name\":\"loc\",\"label\":\"Location\",\"type\":{\"kind\":\"list\",\"elementType\":{\"kind\":\"union\",\"types\":[\"string\",\"integer\"]}},\"description\":\"\",\"required\":true,\"ui\":{}},\"msg\":{\"name\":\"msg\",\"label\":\"Message\",\"type\":\"string\",\"description\":\"\",\"required\":true,\"ui\":{}},\"type\":{\"name\":\"type\",\"label\":\"Error Type\",\"type\":\"string\",\"description\":\"\",\"required\":true,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"msg\\\": \\\"\\\",\\n  \\\"type\\\": \\\"\\\"\\n}\",\"required\":\"{\\n  \\\"msg\\\": \\\"\\\",\\n  \\\"type\\\": \\\"\\\"\\n}\"}},\"HTTPValidationError\":{\"name\":\"HTTPValidationError\",\"description\":\"\",\"properties\":{\"detail\":{\"name\":\"detail\",\"label\":\"Detail\",\"type\":{\"kind\":\"list\",\"elementType\":{\"kind\":\"type\",\"name\":\"ValidationError\"}},\"description\":\"\",\"required\":false,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"detail\\\": [\\n    {\\n      \\\"msg\\\": \\\"\\\",\\n      \\\"type\\\": \\\"\\\"\\n    }\\n  ]\\n}\",\"required\":\"{}\"}}}},\"functionMetadata\":{\"name\":\"generate_video_regular_endpoint\",\"description\":\"Generate Video Regular Endpoint\",\"path\":\"\",\"inputs\":[\"FramePackRequest\"],\"output\":\"FramePackResponse\",\"endpoint\":{\"url\":\"https://fal.run/fal-ai/framepack\",\"method\":\"post\"}},\"inputTypeMetadata\":{\"name\":\"FramePackRequest\",\"description\":\"\",\"properties\":{\"prompt\":{\"name\":\"prompt\",\"label\":\"Prompt\",\"type\":\"string\",\"description\":\"Text prompt for video generation (max 500 characters).\",\"required\":true,\"examples\":[\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\"],\"ui\":{}},\"negative_prompt\":{\"name\":\"negative_prompt\",\"label\":\"Negative Prompt\",\"type\":\"string\",\"description\":\"Negative prompt for video generation. Default value: `\\\"\\\"`\",\"required\":false,\"examples\":[\"Ugly, blurry distorted, bad quality\"],\"defaultValue\":\"\",\"ui\":{}},\"image_url\":{\"name\":\"image_url\",\"label\":\"Image Url\",\"type\":\"string\",\"description\":\"URL of the image input.\",\"required\":true,\"examples\":[\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\"],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed to use for generating the video.\",\"required\":false,\"ui\":{}},\"aspect_ratio\":{\"name\":\"aspect_ratio\",\"label\":\"Aspect Ratio (W:H)\",\"type\":{\"kind\":\"enum\",\"values\":[\"16:9\",\"9:16\"],\"name\":\"AspectRatio(W:H)Enum\"},\"description\":\"The aspect ratio of the video to generate. Default value: `\\\"16:9\\\"`\",\"required\":false,\"enumValues\":[\"16:9\",\"9:16\"],\"defaultValue\":\"16:9\",\"ui\":{}},\"resolution\":{\"name\":\"resolution\",\"label\":\"Resolution\",\"type\":{\"kind\":\"enum\",\"values\":[\"720p\",\"480p\"],\"name\":\"ResolutionEnum\"},\"description\":\"The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations. Default value: `\\\"480p\\\"`\",\"required\":false,\"enumValues\":[\"720p\",\"480p\"],\"defaultValue\":\"480p\",\"ui\":{}},\"cfg_scale\":{\"name\":\"cfg_scale\",\"label\":\"CFG Scale\",\"type\":\"float\",\"description\":\"Classifier-Free Guidance scale for the generation. Default value: `1`\",\"required\":false,\"defaultValue\":1,\"min\":0,\"max\":7,\"ui\":{}},\"guidance_scale\":{\"name\":\"guidance_scale\",\"label\":\"Guidance Scale\",\"type\":\"float\",\"description\":\"Guidance scale for the generation. Default value: `10`\",\"required\":false,\"defaultValue\":10,\"min\":0,\"max\":32,\"ui\":{}},\"num_frames\":{\"name\":\"num_frames\",\"label\":\"Number of Frames\",\"type\":\"integer\",\"description\":\"The number of frames to generate. Default value: `180`\",\"required\":false,\"defaultValue\":180,\"min\":30,\"max\":900,\"ui\":{}},\"enable_safety_checker\":{\"name\":\"enable_safety_checker\",\"label\":\"Enable Safety Checker\",\"type\":\"boolean\",\"description\":\"If set to true, the safety checker will be enabled.\",\"required\":false,\"examples\":[true],\"defaultValue\":false,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"negative_prompt\\\": \\\"Ugly, blurry distorted, bad quality\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\",\\n  \\\"aspect_ratio\\\": \\\"16:9\\\",\\n  \\\"resolution\\\": \\\"480p\\\",\\n  \\\"cfg_scale\\\": 1,\\n  \\\"guidance_scale\\\": 10,\\n  \\\"num_frames\\\": 180,\\n  \\\"enable_safety_checker\\\": true\\n}\",\"required\":\"{\\n  \\\"prompt\\\": \\\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\\\",\\n  \\\"image_url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\\\"\\n}\"}},\"outputTypeMetadata\":{\"name\":\"FramePackResponse\",\"description\":\"\",\"properties\":{\"video\":{\"name\":\"video\",\"label\":\"Video\",\"type\":{\"kind\":\"type\",\"name\":\"File\"},\"description\":\"\",\"required\":true,\"examples\":[{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}],\"ui\":{}},\"seed\":{\"name\":\"seed\",\"label\":\"Seed\",\"type\":\"integer\",\"description\":\"The seed used for generating the video.\",\"required\":true,\"ui\":{}}},\"examples\":{\"all\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\",\"required\":\"{\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\\\"\\n  }\\n}\"}}},\"children\":[\"$\",\"$L22\",null,{\"model\":\"$1d\",\"children\":[\"$\",\"$L23\",null,{\"className\":\"px-0\",\"padding\":\"none\",\"hideHorizontalNavigation\":true,\"breadcrumbs\":[{\"label\":\"Home\",\"href\":\"/\"},{\"label\":\"Explore\",\"href\":\"/models\"},{\"label\":\"fal-ai/framepack\",\"href\":\"/models/fal-ai/framepack\"}],\"children\":[[\"$\",\"$L24\",null,{\"endpointId\":\"fal-ai/framepack\",\"model\":\"$1d\",\"appData\":\"$25\",\"modes\":\"$e6\",\"metadata\":\"$f6\",\"functionMetadata\":\"$1ad\",\"inputTypeMetadata\":\"$1b0\",\"outputTypeMetadata\":\"$1d1\",\"endpointBilling\":{\"endpoint\":\"fal-ai/framepack\",\"billing_unit\":\"seconds\",\"price\":0.0333,\"provider_type\":\"fal\",\"is_partner_api\":false,\"balance_check\":false}}],[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-6\",\"children\":[\"$\",\"$L10\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"models\",\"children\",\"$1db\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L11\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]}]]}]}]}]\n"])</script><script>self.__next_f.push([1,"1dc:I[84237,[\"28410\",\"static/chunks/55eb4b32-2e00cf239b0428f3.js\",\"35878\",\"static/chunks/9da6db1e-c3308e728af630be.js\",\"95031\",\"static/chunks/95031-9b62a533099eace7.js\",\"69902\",\"static/chunks/69902-37f48df72b80a01b.js\",\"27648\",\"static/chunks/27648-caa9b493a47dc629.js\",\"37813\",\"static/chunks/37813-252fea9de889c305.js\",\"49095\",\"static/chunks/49095-d13a2f3baad06e93.js\",\"96845\",\"static/chunks/96845-ea33a2169b1c7df2.js\",\"29763\",\"static/chunks/29763-5ac3d808e0a9620d.js\",\"76798\",\"static/chunks/76798-45c36fef3e62663c.js\",\"82957\",\"static/chunks/82957-aef5ae7f4ee19098.js\",\"71632\",\"static/chunks/71632-69addefea7ce37b8.js\",\"39643\",\"static/chunks/39643-4b935f0b42608a0e.js\",\"28119\",\"static/chunks/28119-89b8f055927fcbad.js\",\"26470\",\"static/chunks/26470-6c5f3171f6709917.js\",\"89105\",\"static/chunks/89105-f233706d7cccd674.js\",\"88447\",\"static/chunks/88447-702256d80b07eb23.js\",\"68335\",\"static/chunks/68335-e855da731ea75d69.js\",\"51755\",\"static/chunks/51755-09ce4f3b30cceebd.js\",\"62266\",\"static/chunks/62266-393fbff85279c7d7.js\",\"85281\",\"static/chunks/85281-6654c5be68c11c67.js\",\"18642\",\"static/chunks/18642-3631ad70a3aeac91.js\",\"37728\",\"static/chunks/37728-ac11742159386b0d.js\",\"53597\",\"static/chunks/53597-f7f9b94a2f1025b9.js\",\"73892\",\"static/chunks/73892-eb7b6ccbc0fd6e5e.js\",\"56470\",\"static/chunks/56470-163b5a2d4c9d0a75.js\",\"31229\",\"static/chunks/31229-42e6c705eb176915.js\",\"77598\",\"static/chunks/77598-68f29a5770332f81.js\",\"13590\",\"static/chunks/13590-f69849ac25e543d3.js\",\"26092\",\"static/chunks/26092-f2c8b6dd807de6d8.js\",\"29229\",\"static/chunks/29229-043c9cfe4b16078c.js\",\"99191\",\"static/chunks/99191-d2c2ef8c21ffe964.js\",\"88263\",\"static/chunks/88263-8333c74b0d352b20.js\",\"41041\",\"static/chunks/41041-eaaefc50183d521c.js\",\"56370\",\"static/chunks/56370-119ae49b3a84589b.js\",\"35826\",\"static/chunks/35826-be7913f8d388e6a5.js\",\"94160\",\"static/chunks/94160-6b84624a83f5d264.js\",\"86772\",\"static/chunks/86772-e38b24914078b175.js\",\"18996\",\"static/chunks/18996-a42af8b4451c07c7.js\",\"519\",\"static/chunks/519-feefe8605dc8ab1f.js\",\"68296\",\"static/chunks/68296-ff11535029d6dfbf.js\",\"35732\",\"static/chunks/35732-cb2a30b2b2a03a7e.js\",\"55193\",\"static/chunks/app/models/%5BendpointId%5D/page-350c892e7ff2b32a.js\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"e:[\"$\",\"$L1dc\",null,{\"initialInput\":{\"prompt\":\"A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike.\",\"negative_prompt\":\"Ugly, blurry distorted, bad quality\",\"image_url\":\"https://storage.googleapis.com/falserverless/framepack/framepack.jpg\",\"aspect_ratio\":\"16:9\",\"resolution\":\"480p\",\"cfg_scale\":1,\"guidance_scale\":10,\"num_frames\":180,\"enable_safety_checker\":true},\"requestData\":{\"json_output\":{\"video\":{\"url\":\"https://storage.googleapis.com/falserverless/framepack/TfJPbwm6_D60dcWEv9LVX_output_video.mp4\"}}},\"minimumUnits\":null,\"trainingHistory\":[],\"endpointBilling\":{\"endpoint\":\"fal-ai/framepack\",\"billing_unit\":\"seconds\",\"price\":0.0333,\"provider_type\":\"fal\",\"is_partner_api\":false,\"balance_check\":false}}]\n"])</script></body></html>