# Ignore Python cache and compiled files
*.pyc
*.pyo
*.pyd
__pycache__/
.ipynb_checkpoints/

# Ignore distribution build artifacts
.dist/
dist/
build/
*.egg-info/

# Ignore IDE/Editor specific files
.idea/
.vscode/
*.swp
*.swo

# Ignore OS generated files
.DS_Store
Thumbs.db

# Ignore virtual environment directories
.venv/
venv/
*/.venv/
*/venv/
env/
*/env/

# Ignore sensitive environment file
.env

# Ignore generated output and user data directories
outputs/
reference_audio/
voices/
temp_audio_outputs/
model_cache/ # Also good practice to ignore the model cache

# Ignore test reports/coverage
.coverage
htmlcov/
.pytest_cache/

# Ignore log files
*.log
