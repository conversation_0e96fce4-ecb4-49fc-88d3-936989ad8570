{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noImplicitAny": false, "ignoreDeprecations": "5.0", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "baseUrl": ".", "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "tests/**/*"]}