# requirements.txt

chatterbox-tts

# Core Web Framework
fastapi
uvicorn[standard]

# Machine Learning & Audio
torch @ https://repo.radeon.com/rocm/manylinux/rocm-rel-6.4.1/torch-2.6.0%2Brocm6.4.1.git1ded221d-cp310-cp310-linux_x86_64.whl
torchaudio @ https://repo.radeon.com/rocm/manylinux/rocm-rel-6.4.1/torchaudio-2.6.0%2Brocm6.4.1.gitd8831425-cp310-cp310-linux_x86_64.whl
pytorch-triton-rocm @ https://repo.radeon.com/rocm/manylinux/rocm-rel-6.4.1/pytorch_triton_rocm-3.2.0%2Brocm6.4.1.git6da9e660-cp310-cp310-linux_x86_64.whl
numpy
soundfile # Requires libsndfile system library (e.g., sudo apt-get install libsndfile1 on Debian/Ubuntu)
huggingface_hub
descript-audio-codec
safetensors

# Configuration & Utilities
pydantic
python-dotenv # Used ONLY for initial config seeding if config.yaml missing
Jinja2
python-multipart # For file uploads
requests # For health checks or other potential uses
PyYAML # For parsing presets.yaml AND primary config.yaml
tqdm

# Audio Post-processing
pydub
praat-parselmouth # For unvoiced segment removal
librosa # for changes to sampling
hf-transfer
