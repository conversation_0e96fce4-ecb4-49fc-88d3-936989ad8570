{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "authorship_tag": "ABX9TyMPz9zx+QHncUElynmlMUGr"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "lcQFeUm-0tY6"}, "outputs": [], "source": []}, {"cell_type": "code", "source": ["# @title 1. Setup and Installation (Robust Version)\n", "# Clone repository and install dependencies with error handling\n", "\n", "# Remove existing directory and clear cache\n", "!rm -rf Chatterbox-TTS-Server\n", "!pip cache purge\n", "\n", "# Clone repository\n", "!git clone https://github.com/devnen/Chatterbox-TTS-Server.git\n", "%cd Chatterbox-TTS-Server\n", "\n", "print(\"✅ Repository cloned. Installing core dependencies...\")\n", "\n", "# Install PyTorch with compatible torchvision\n", "!pip install torch==2.5.1+cu121 torchaudio==2.5.1+cu121 torchvision==0.20.1+cu121 --index-url https://download.pytorch.org/whl/cu121 -q\n", "\n", "# Install your Colab-compatible chatterbox fork\n", "!pip install git+https://github.com/devnen/chatterbox.git -q\n", "\n", "print(\"✅ Core TTS components installed. Installing server dependencies...\")\n", "\n", "# Install essential server requirements (skip problematic packages)\n", "!pip install fastapi uvicorn pyyaml soundfile librosa safetensors -q\n", "!pip install python-multipart requests jinja2 watchdog aiofiles unidecode inflect tqdm -q\n", "!pip install pydub audiotsm -q\n", "\n", "# Try to install parselmouth (may fail on some systems)\n", "!pip install parselmouth -q || echo \"Parselmouth installation failed - unvoiced segment removal will be disabled\"\n", "\n", "print(\"✅ Installation complete! Some optional packages may have been skipped.\")"], "metadata": {"id": "3ghls0ts1PH_"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# @title 2. Launch the Server! ⚡\n", "# Starts the TTS server with memory optimization\n", "\n", "import threading\n", "import time\n", "import uvicorn\n", "import gc\n", "from google.colab.output import serve_kernel_port_as_window\n", "\n", "port = 8004\n", "\n", "print(\"🛑 Stopping existing servers...\")\n", "!fuser -k 8004/tcp 2>/dev/null || echo \"Port clear\"\n", "!pkill -f \"uvicorn.*server\" 2>/dev/null || echo \"No running servers\"\n", "\n", "print(\"🧹 Clearing memory...\")\n", "gc.collect()\n", "\n", "%cd /content/Chatterbox-TTS-Server\n", "\n", "def run_server():\n", "    try:\n", "        from server import app\n", "        print(\"✅ Server starting...\")\n", "        # Add memory-efficient settings\n", "        uvicorn.run(\n", "            app,\n", "            host=\"0.0.0.0\",\n", "            port=port,\n", "            log_level=\"warning\",  # Reduce log verbosity\n", "            access_log=False      # Disable access logs to save memory\n", "        )\n", "    except Exception as e:\n", "        print(f\"❌ Server error: {e}\")\n", "\n", "print(\"🚀 Starting TTS server...\")\n", "\n", "thread = threading.Thread(target=run_server, daemon=True)\n", "thread.start()\n", "\n", "time.sleep(25)  # Extra time for model loading\n", "\n", "print(\"🎉 Server ready! Click below:\")\n", "serve_kernel_port_as_window(port)\n", "\n", "print(\"\\n💡 Tips to prevent crashes:\")\n", "print(\"• Use shorter texts (under 500 characters)\")\n", "print(\"• Wait for each generation to complete before starting another\")\n", "print(\"• If it crashes, just re-run this cell\")"], "metadata": {"id": "Wm_FHPwV1Zkx"}, "execution_count": null, "outputs": []}]}